/************************头 文 件******************************/
#include <context.h>
#include <cp15.h>
#include <cpu.h>
#include <cpuid.h>
#include <mmu.h>
#include <msr.h>
#include <period_sched_group.h>
#include <process_signal.h>
#include <ptrace/ptrace.h>
#include <signal.h>
#include <stdint.h>
#include <stdio.h>
#include <syscalls.h>
#include <ttos.h>
#include <ttosInterTask.inl>
#include <ttosProcess.h>
#include <ttos_pic.h>
#include <uaccess.h>
#include <vector.h>

#include <trace/tracing.h>
#undef KLOG_TAG
#define KLOG_TAG "Exception"
#include <klog.h>

/************************宏 定 义******************************/
#define IDT_MAX_NUM 256
#define INTERRUPT_GATE 0xE // 中断门
#define TRAP_GATE 0xF      // 陷阱门

#define IA32_STAR 0xC0000081
#define IA32_LSTAR 0xC0000082
#define IA32_FMASK 0xC0000084

/************************类型定义******************************/
/* idt gate descriptors*/
struct gate_desc
{
    uint32_t offset0 : 16;
    uint32_t segment_selector : 16;
    uint32_t ist : 3;
    uint32_t fixed_value_bits : 5;
    uint32_t gate_type : 5;
    uint32_t privilege : 2;
    uint32_t present : 1;
    uint32_t offset1 : 16;
    uint32_t offset2;
    uint32_t reserved;
};

typedef void (*default_handler)(void);

/************************外部声明******************************/
void restore_context(void *context);
extern size_t intNestLevel[CONFIG_MAX_CPUS];
void ttosSchedule(void);
extern syscall_func syscall_table[CONFIG_SYSCALL_NUM];
extern syscall_func syscall_extent_table[CONFIG_EXTENT_SYSCALL_NUM];
void backtrace_r(const char *cookie, uintptr_t frame_address);
extern void kernel_set_tls(uintptr_t tls);
void arch_signal_quit(arch_exception_context_t *context);
extern void ptrace_cancel_bpt(pcb_t pcb);
void do_work_pending(void *exp_frame);
void restore_raw_context(arch_exception_context_t *context);
void restore_hw_debug(pcb_t pcb);
extern void syscall_entry(void);
void arch_fp_context_save(void *fp_context);
void arch_fp_context_restore(void *fp_context);
/************************前向声明******************************/
/************************模块变量******************************/
static struct gate_desc idt[IDT_MAX_NUM] __attribute__((__aligned__(8))) = {{0}};

static const default_handler default_handler_table[IDT_MAX_NUM] = {
    exception_entry_0,       exception_entry_1,       exception_entry_2,
    exception_entry_3,       exception_entry_4,       exception_entry_5,
    exception_entry_6,       exception_entry_7,       exception_entry_8,
    exception_entry_9,       exception_entry_10,      exception_entry_11,
    exception_entry_12,      exception_entry_13,      exception_entry_14,
    default_exception_entry, exception_entry_16,      exception_entry_17,
    exception_entry_18,      exception_entry_19,      default_exception_entry,
    default_exception_entry, default_exception_entry, default_exception_entry,
    default_exception_entry, default_exception_entry, default_exception_entry,
    default_exception_entry, default_exception_entry, default_exception_entry,
    default_exception_entry, default_exception_entry, interrupt_entry_32,
    interrupt_entry_33,      interrupt_entry_34,      interrupt_entry_35,
    interrupt_entry_36,      interrupt_entry_37,      interrupt_entry_38,
    interrupt_entry_39,      interrupt_entry_40,      interrupt_entry_41,
    interrupt_entry_42,      interrupt_entry_43,      interrupt_entry_44,
    interrupt_entry_45,      interrupt_entry_46,      interrupt_entry_47,
    interrupt_entry_48,      interrupt_entry_49,      interrupt_entry_50,
    interrupt_entry_51,      interrupt_entry_52,      interrupt_entry_53,
    interrupt_entry_54,      interrupt_entry_55,      interrupt_entry_56,
    interrupt_entry_57,      interrupt_entry_58,      interrupt_entry_59,
    interrupt_entry_60,      interrupt_entry_61,      interrupt_entry_62,
    interrupt_entry_63,      interrupt_entry_64,      interrupt_entry_65,
    interrupt_entry_66,      interrupt_entry_67,      interrupt_entry_68,
    interrupt_entry_69,      interrupt_entry_70,      interrupt_entry_71,
    interrupt_entry_72,      interrupt_entry_73,      interrupt_entry_74,
    interrupt_entry_75,      interrupt_entry_76,      interrupt_entry_77,
    interrupt_entry_78,      interrupt_entry_79,      interrupt_entry_80,
    interrupt_entry_81,      interrupt_entry_82,      interrupt_entry_83,
    interrupt_entry_84,      interrupt_entry_85,      interrupt_entry_86,
    interrupt_entry_87,      interrupt_entry_88,      interrupt_entry_89,
    interrupt_entry_90,      interrupt_entry_91,      interrupt_entry_92,
    interrupt_entry_93,      interrupt_entry_94,      interrupt_entry_95,
    interrupt_entry_96,      interrupt_entry_97,      interrupt_entry_98,
    interrupt_entry_99,      interrupt_entry_100,     interrupt_entry_101,
    interrupt_entry_102,     interrupt_entry_103,     interrupt_entry_104,
    interrupt_entry_105,     interrupt_entry_106,     interrupt_entry_107,
    interrupt_entry_108,     interrupt_entry_109,     interrupt_entry_110,
    interrupt_entry_111,     interrupt_entry_112,     interrupt_entry_113,
    interrupt_entry_114,     interrupt_entry_115,     interrupt_entry_116,
    interrupt_entry_117,     interrupt_entry_118,     interrupt_entry_119,
    interrupt_entry_120,     interrupt_entry_121,     interrupt_entry_122,
    interrupt_entry_123,     interrupt_entry_124,     interrupt_entry_125,
    interrupt_entry_126,     interrupt_entry_127,     interrupt_entry_128,
    interrupt_entry_129,     interrupt_entry_130,     interrupt_entry_131,
    interrupt_entry_132,     interrupt_entry_133,     interrupt_entry_134,
    interrupt_entry_135,     interrupt_entry_136,     interrupt_entry_137,
    interrupt_entry_138,     interrupt_entry_139,     interrupt_entry_140,
    interrupt_entry_141,     interrupt_entry_142,     interrupt_entry_143,
    interrupt_entry_144,     interrupt_entry_145,     interrupt_entry_146,
    interrupt_entry_147,     interrupt_entry_148,     interrupt_entry_149,
    interrupt_entry_150,     interrupt_entry_151,     interrupt_entry_152,
    interrupt_entry_153,     interrupt_entry_154,     interrupt_entry_155,
    interrupt_entry_156,     interrupt_entry_157,     interrupt_entry_158,
    interrupt_entry_159,     interrupt_entry_160,     interrupt_entry_161,
    interrupt_entry_162,     interrupt_entry_163,     interrupt_entry_164,
    interrupt_entry_165,     interrupt_entry_166,     interrupt_entry_167,
    interrupt_entry_168,     interrupt_entry_169,     interrupt_entry_170,
    interrupt_entry_171,     interrupt_entry_172,     interrupt_entry_173,
    interrupt_entry_174,     interrupt_entry_175,     interrupt_entry_176,
    interrupt_entry_177,     interrupt_entry_178,     interrupt_entry_179,
    interrupt_entry_180,     interrupt_entry_181,     interrupt_entry_182,
    interrupt_entry_183,     interrupt_entry_184,     interrupt_entry_185,
    interrupt_entry_186,     interrupt_entry_187,     interrupt_entry_188,
    interrupt_entry_189,     interrupt_entry_190,     interrupt_entry_191,
    interrupt_entry_192,     interrupt_entry_193,     interrupt_entry_194,
    interrupt_entry_195,     interrupt_entry_196,     interrupt_entry_197,
    interrupt_entry_198,     interrupt_entry_199,     interrupt_entry_200,
    interrupt_entry_201,     interrupt_entry_202,     interrupt_entry_203,
    interrupt_entry_204,     interrupt_entry_205,     interrupt_entry_206,
    interrupt_entry_207,     interrupt_entry_208,     interrupt_entry_209,
    interrupt_entry_210,     interrupt_entry_211,     interrupt_entry_212,
    interrupt_entry_213,     interrupt_entry_214,     interrupt_entry_215,
    interrupt_entry_216,     interrupt_entry_217,     interrupt_entry_218,
    interrupt_entry_219,     interrupt_entry_220,     interrupt_entry_221,
    interrupt_entry_222,     interrupt_entry_223,     interrupt_entry_224,
    interrupt_entry_225,     interrupt_entry_226,     interrupt_entry_227,
    interrupt_entry_228,     interrupt_entry_229,     interrupt_entry_230,
    interrupt_entry_231,     interrupt_entry_232,     interrupt_entry_233,
    interrupt_entry_234,     interrupt_entry_235,     interrupt_entry_236,
    interrupt_entry_237,     interrupt_entry_238,     interrupt_entry_239,
    interrupt_entry_240,     interrupt_entry_241,     interrupt_entry_242,
    interrupt_entry_243,     interrupt_entry_244,     interrupt_entry_245,
    interrupt_entry_246,     interrupt_entry_247,     interrupt_entry_248,
    interrupt_entry_249,     interrupt_entry_250,     interrupt_entry_251,
    interrupt_entry_252,     interrupt_entry_253,     interrupt_entry_254,
    interrupt_entry_255};

static const char *fault_info[] = {"Divide Error",
                                   "Debug Exception",
                                   "NMI Interrupt",
                                   "Breakpoint",
                                   "Overflow",
                                   "BOUND Range Exceeded",
                                   "Invalid Opcode",
                                   "Device Not Available",
                                   "Double Fault",
                                   "Coprocessor Segment Overrun",
                                   "Invalid TSS",
                                   "Segment Not Present",
                                   "Stack-Segment Fault",
                                   "General Protection",
                                   "Page Fault",
                                   "reserved",
                                   "x87 FPU Floating-Point Error",
                                   "Alignment Check",
                                   "Machine Check",
                                   "SIMD Floating-Point Exception",
                                   "Virtualization Exception",
                                   "Control Protection Exception"};

/************************全局变量******************************/
/************************函数实现******************************/

static bool is_user_mode(arch_int_context_t *context)
{
    return (context->cs == USER_CODE64);
}

void align_error(void)
{
    printk("fp context align error\n");
    while (1)
        ;
}

static void idt_set(struct gate_desc *base)
{
    struct desc_ptr idtr;
    idtr.limit = (sizeof(struct gate_desc) * IDT_MAX_NUM) - 1;
    idtr.base = (uintptr_t)base;
    asm volatile("lidt %0" ::"m"(idtr));
}

static struct gate_desc idt_gate_desc_init(void *handler, uint8_t type, uint8_t pri, uint8_t ist)
{
    struct gate_desc desc = {
        .offset0 = (((size_t)handler) & 0xffff),
        .segment_selector = cs_get(),
        .ist = ist,
        .fixed_value_bits = 0,
        .gate_type = type,
        .privilege = pri,
        .present = 1,
        .offset1 = ((((size_t)handler) >> 16) & 0xffff),
        .offset2 = (((size_t)handler) >> 32),
        .reserved = 0,
    };
    return desc;
}

/**
 * @brief
 *    设置该上下文类型是否是系统调用上下文
 * @param[in] context 上下文
 * @retval 无
 */
void set_context_type(struct arch_context *context, int type)
{
    context->type = type;
    context->ori_rax = context->rax;
}

static void save_exce_context(pcb_t pcb, struct arch_context *context)
{
    memcpy(&pcb->exception_context, context, sizeof(pcb->exception_context));
}

void syscall_stack_set(void *stack)
{
    void kernel_gs_base_set(unsigned long base);
    kernel_gs_base_set((unsigned long)stack);
    *(unsigned long *)stack = (unsigned long)stack;
}

void syscall_init(void)
{
    /* 设置系统调用入口地址 */
    msr_write(IA32_LSTAR, (uint64_t)syscall_entry);

    /* 设置系统调用指令(syscall)产生后，加载的cs和ss,由于产生系统调用后,硬件会将IA32_STAR[47:32]+8
     * -> ss,所以需要-8 */
    msr_write(IA32_STAR, (KERNEL_CODE64) << 32UL | (KERNEL_DATA64 - 8) << 48UL);

    /* 设置系统调用指令(syscall)产生后，以下标志位会被清0 */
    msr_write(IA32_FMASK, X86_EFLAGS_CF | X86_EFLAGS_PF | X86_EFLAGS_AF | X86_EFLAGS_ZF |
                              X86_EFLAGS_SF | X86_EFLAGS_TF | X86_EFLAGS_IF | X86_EFLAGS_DF |
                              X86_EFLAGS_OF | X86_EFLAGS_IOPL | X86_EFLAGS_NT | X86_EFLAGS_RF |
                              X86_EFLAGS_AC | X86_EFLAGS_ID);
}

/**
 * @brief
 *    初始化异常向量
 * @param[in] context 异常上下文
 * @param[in] vector 异常号
 * @retval 无
 */
void bp_exception_init(void)
{
    /* 主核需要设置idt表(从核只需要加载使用) */
    for (size_t i = 0; i < IDT_MAX_NUM; i++)
    {
        idt[i] = idt_gate_desc_init(default_handler_table[i], INTERRUPT_GATE, 3, 0);
    }

    idt_set(idt);

    syscall_init();
}

void ap_exception_init(void)
{
    /* 主核已经设置好idt表，从核只需要加载使用 */
    idt_set(idt);

    syscall_init();
}

static bool is_usr_debug_exception(uint64_t vector, bool usr_exception)
{
    return (usr_exception && (GENERAL_DB_INT == vector || GENERAL_TRAP_INT == vector));
}

static bool is_illins_exception(uint64_t vector)
{
    return (GENERAL_ILLINS_INT == vector);
}

void debug_handle(uint64_t vector, pcb_t pcb, arch_exception_context_t *context)
{
    ksiginfo_t siginfo;

    siginfo.si_addr = (void *)context->rip;
    siginfo.si_code = 0x0004;
    if (pcb != NULL)
    {
        if (pcb->group_leader->ptrace & PT_SINGLESTEP)
        {
            /* 取消所有断点 */
            ptrace_cancel_bpt(pcb);
        }
        /* 向当前线程发送SIGTRAP信号 */
        kernel_signal_kill(pcb->taskControlId->tid, TO_THREAD, SIGTRAP, siginfo.si_code, &siginfo);

        /* 恢复上下文会做信号检测 */
        set_context_type(&pcb->exception_context, EXCEPTION_CONTEXT);
        restore_context(&pcb->exception_context);
    }
}

void usr_exception_handle(TASK_ID task, arch_exception_context_t *context, uint64_t vector)
{
#if 0
    /* 非法指令异常 */
    if (is_illins_exception(vector))
    {
        pid_t pid = get_process_pid((pcb_t)task->ppcb);
        kernel_signal_kill(pid, TO_PROCESS, SIGILL, SI_KERNEL, NULL);
        set_context_type(context, EXCEPTION_CONTEXT);
        restore_context(context);
    }
    else
    {
        pcb_t pcb = (pcb_t)task->ppcb;
        pid_t pid = get_process_pid((pcb_t)task->ppcb);

        kernel_signal_kill(pid, TO_PROCESS, info->sig, info->code, NULL);
        set_context_type(context, EXCEPTION_CONTEXT);

        /* 避免返回到用户态的上下文信息被破坏 */
        if (user_context_valid(context))
        {
            restore_context(context);
        }
    }
#endif
}

bool is_usr_exception(arch_exception_context_t *context, TASK_ID task)
{
    return (context->cs == USER_CODE64 && task && task->ppcb);
}

static void exception_context_show(arch_exception_context_t *context, uint64_t vector)
{
    KLOG_EMERG("%s exception", context->cs == USER_CODE64 ? "usr" : "kernel");
    KLOG_EMERG("exception cpu:%d\n", cpuid_get());
    KLOG_EMERG("exception %d:%s", vector, fault_info[vector]);
    KLOG_EMERG("RegMap:");
    KLOG_EMERG("ss     :%p", (void *)context->ss);
    KLOG_EMERG("rsp    :%p", (void *)context->rsp);
    KLOG_EMERG("rflags :%p", (void *)context->rflags);
    KLOG_EMERG("cs     :%p", (void *)context->cs);
    KLOG_EMERG("rip    :%p", (void *)context->rip);

    KLOG_EMERG("errorCode    :%p", (void *)context->errorCode);

    KLOG_EMERG("rax    :%p", (void *)context->rax);
    KLOG_EMERG("rcx    :%p", (void *)context->rcx);
    KLOG_EMERG("rdx    :%p", (void *)context->rdx);
    KLOG_EMERG("rsi    :%p", (void *)context->rsi);
    KLOG_EMERG("rdi    :%p", (void *)context->rdi);
    KLOG_EMERG("r8     :%p", (void *)context->r8);
    KLOG_EMERG("r9     :%p", (void *)context->r9);
    KLOG_EMERG("r10    :%p", (void *)context->r10);
    KLOG_EMERG("r11    :%p", (void *)context->r11);
    KLOG_EMERG("rbx    :%p", (void *)context->rbx);
    KLOG_EMERG("rbp    :%p", (void *)context->rbp);
    KLOG_EMERG("r12    :%p", (void *)context->r12);
    KLOG_EMERG("r13    :%p", (void *)context->r13);
    KLOG_EMERG("r14    :%p", (void *)context->r14);
    KLOG_EMERG("r15    :%p", (void *)context->r15);
}

/**
 * @brief
 *    异常处理程序
 * @param[in] context 异常上下文
 * @param[in] vector 异常号
 * @retval 无
 */
void do_exception(arch_exception_context_t *context, uint64_t vector)
{
    pcb_t pcb = NULL;
    TASK_ID task = ttosGetRunningTask();
    bool usr_exception = false;

    if (is_usr_exception(context, task))
    {
        pcb = (pcb_t)task->ppcb;
        save_exce_context(pcb, context);
        TTOS_TaskEnterKernelHook(task);
        usr_exception = true;
    }

    if (is_usr_debug_exception(vector, usr_exception))
    {
        debug_handle(vector, pcb, context);
    }

    exception_context_show(context, vector);

    if (usr_exception)
    {
        usr_exception_handle(task, context, vector);
    }
    else
    {
        TTOS_SuspendTask(task);
    }

    while (1)
        ;
}

/**
 * @brief
 *    中断处理程序
 * @param[in] context 中断上下文
 * @param[in] vector 中断号
 * @retval 无
 */
void do_irq(arch_int_context_t *context, uint64_t vector)
{
    s32 ret = 0;
    u32 from_cpu;
    u32 irq = vector;
    s32 cpuid = 0;

#ifdef _HARD_FLOAT_
    arch_fp_context_save(context->fp_save_area);
#endif

    TASK_ID task = ttosGetRunningTask();
    pcb_t pcb = NULL;

    if (is_user_mode(context) && task && task->ppcb)
    {
        pcb = (pcb_t)task->ppcb;
        save_exce_context(pcb, context);
        TTOS_TaskEnterKernelHook(task);
    }

    cpuid = cpuid_get();

    ret = ttos_pic_irq_ack(&from_cpu, &irq);
    if (ret == 0)
    {
        KLOG_I("irq happend irq:%d from cpu:%d", irq, from_cpu);
        TRACING_EVENT_ENTER(isr, irq, from_cpu);

        ttosDisableScheduleLevel[cpuid]++;
        intNestLevel[cpuid]++;

        ttos_pic_irq_handle(irq);

        ttos_pic_irq_eoi(irq, from_cpu);

        intNestLevel[cpuid]--;
        ttosDisableScheduleLevel[cpuid]--;

        TRACING_EVENT_EXIT(isr, irq);
    }

    ttosSchedule();

    set_context_type(context, IRQ_CONTEXT);

    if (is_user_mode(context))
    {
        pcb_t pcb = ttosProcessSelf();
        do_work_pending(context);
        if (pcb->group_leader->ptrace)
        {
            restore_hw_debug(pcb->group_leader);
        }

        TTOS_TaskEnterUserHook(pcb->taskControlId);
    }

#ifdef _HARD_FLOAT_
    asm volatile("cli");
    arch_fp_context_restore(context->fp_save_area);
#endif
}

/**
 * @brief
 *    syscall处理程序
 * @param[in] context 系统调用上下文
 * @retval 无
 */
void do_syscall(arch_exception_context_t *context)
{
    int64_t ret = 0;
    int32_t syscall_num = 0;

    TASK_ID task = ttosGetRunningTask();
    pcb_t pcb = NULL;

    if (is_user_mode(context) && task && task->ppcb)
    {
        pcb = (pcb_t)task->ppcb;
        save_exce_context(pcb, context);
        TTOS_TaskEnterKernelHook(task);
    }

    syscall_num = context->rax;

    set_context_type(context, SYSCALL_CONTEXT);

    arch_cpu_int_enable();

    if (is_extent_syscall_num(syscall_num))
    {
        syscall_num -= CONFIG_EXTENT_SYSCALL_NUM_START;
        if (0 == syscall_num)
        {
            ret = syscall_extent_table[syscall_num]((long)context, 0, 0, 0, 0, 0);
        }
        else
        {
            ret = syscall_extent_table[syscall_num](context->rdi, context->rsi, context->rdx,
                                                    context->r10, context->r8, context->r9);
        }

        context->rax = ret & 0xffffffff;
        return;
    }

    if (syscall_num >= CONFIG_SYSCALL_NUM)
    {
        KLOG_I("syscall num %d great than:%d\n", syscall_num, CONFIG_SYSCALL_NUM - 1);
    }
    else
    {
        if (syscall_num == __NR_rt_sigreturn)
        {
            /* 信号hander调用完毕的返回系统调用 */
            rt_sigreturn(context);
        }
        else if (syscall_table[syscall_num])
        {
            TRACING_EVENT_ENTER(syscall, syscall_num, syscall_getname(syscall_num), context->rdi,
                                context->rsi, context->rdx, context->r10, context->r8, context->r9);

            ret = syscall_table[syscall_num](context->rdi, context->rsi, context->rdx, context->r10,
                                             context->r8, context->r9);

            context->rax = ret;
            TRACING_EVENT_EXIT(syscall, syscall_getname(syscall_num), context->rax);
        }
        else
        {
            KLOG_E("syscall_table[%d] is NULL\n", syscall_num);
        }
    }

    if (is_user_mode(context))
    {
        pcb_t pcb = ttosProcessSelf();
        do_work_pending(context);
        if (pcb->group_leader->ptrace)
        {
            restore_hw_debug(pcb->group_leader);
        }

        TTOS_TaskEnterUserHook(pcb->taskControlId);
    }
}
