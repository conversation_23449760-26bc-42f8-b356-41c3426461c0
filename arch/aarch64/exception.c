/**
 * @file    arch/arm/exception.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * e880932c 2024-07-23
 * coredump改为传入所有映射过的用户态地址内容，修复无法bt回溯的问题 b182ca5c
 * 2024-07-17
 * coredump功能支持：在用户态发生异常时保存异常任务现场，并通过gdb查看 914d9323
 * 2024-07-11 添加周期任务支持 ac37f7cc 2024-07-01 1. 增加log模式的trace信息 2.
 * 修改部分tracepoint确保关调度时统计 3511844a 2024-07-02
 * 移除include路径中的trace ac006b61 2024-07-02 移除一级ttos目录 340ba810
 * 2024-07-01 修改拼写错误 f67a5be3 2024-06-26 1. trace ctf格式编码正常，packet
 * header中增加cpu id 2. 添加中断入口的trace点 3.
 * 优化log格式下文件的写入信息处理 4. 优化文件后端写入处理 71e9846b 2024-06-24
 * 添加栈溢出处理时使用临时栈 4a600b1a 2024-06-20 优化中断控制器处理流程
 * df703b2a 2024-06-19 1. trace event模板优化 2.
 * 新增跟踪mutex、fork、中断的tracepoint 3. tracing_log.h不再依赖ttos.h头文件
 * 51be2d21 2024-06-17 Trace模块调整syscall的记录格式
 * 79314f75 2024-06-06 Trace在内核中添加模块、系统调用的trace记录点,
 * 优化部分代码 ecc8bdb4 2024-06-12 异常打印中加入对task的判断
 * 避免在启动第一个任务前进入异常时无法打印错误信息 dba221b9 2024-06-06
 * 添加KLOG的紧急消息以提供立即输出 865ecac5 2024-05-23 正确处理进程异常
 * 3823374e 2024-05-23 添加signal功能模块
 * 6fd7a00d 2024-05-22 整理libk并格式化代码
 * 61252be5 2024-05-22 丰富异常信息
 * be1a0b92 2024-05-21 irq命名修改为ttos_pic_xxx, 头文件引用删<irq.h>
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * 82306706 2024-05-10 修复破坏r1寄存器
 * 5d151716 2024-05-09 增加异常时打印栈信息
 * 50f107ed 2024-05-09 文件系统挂载成功
 * ce6e5b00 2024-05-09 更新异常打印的信息和抛异常的顺序
 * 84020ca7 2024-04-29 添加 vfork系统调用
 * fe0becbf 2024-04-28 增加fork系统调用
 * b5bfac3d 2024-04-26 增加TRY CATCH功能
 * 9a1db972 2024-04-25 修复异常不打印问题
 * f4560b75 2024-04-23 更正文件命名
 * 13664d97 2024-04-23 elf运行成功添加临时测试的elf文件
 * edab30a3 2024-04-19 添加backtrace同时解决path中一处错误的指针释放
 * dd26a3f2 2024-04-01 添加系统调用表
 * 89405763 2024-03-27 移除日志中\n 全部修改为日志输出
 * c7bbbfca 2024-03-18 提交任务功能模块
 * 43b302d7 2024-03-13 添加中断、自旋锁、原子操作相关功能实现。
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

/************************头 文 件******************************/
#include "ttosMM.h"
#include <context.h>
#include <cp15.h>
#include <cpu.h>
#include <process_signal.h>
#include <ptrace/ptrace.h>
#include <signal.h>
#include <stdint.h>
#include <stdio.h>
#include <syscalls.h>
#include <ttos.h>
#include <ttosInterTask.inl>
#include <ttosProcess.h>
#include <ttos_pic.h>

#include <esr.h>
#include <inttypes.h>
#include <symtab.h>
#include <uaccess.h>

#include <trace/tracing.h>

#define KLOG_LEVEL KLOG_INFO
#undef KLOG_TAG
#define KLOG_TAG "Exception"
#include <klog.h>

/************************宏 定 义******************************/
/************************类型定义******************************/

struct fault_info
{
    int sig;
    int code;
    const char *name;
};

/************************外部声明******************************/
void restore_context(void *context);
extern size_t intNestLevel[CONFIG_MAX_CPUS];
void ttosSchedule(void);
extern syscall_func syscall_table[CONFIG_SYSCALL_NUM];
extern syscall_func syscall_extent_table[CONFIG_EXTENT_SYSCALL_NUM];
void backtrace_r(const char *cookie, uintptr_t frame_address);
extern void kernel_set_tls(uintptr_t tls);
void arch_signal_quit(arch_exception_context_t *context);
extern void ptrace_cancel_bpt(pcb_t pcb);

/************************前向声明******************************/
bool stack_overflow(void);
/************************模块变量******************************/

static const struct fault_info fault_info[] = {
    {SIGKILL, SI_KERNEL, "ttbr address size fault"},
    {SIGKILL, SI_KERNEL, "level 1 address size fault"},
    {SIGKILL, SI_KERNEL, "level 2 address size fault"},
    {SIGKILL, SI_KERNEL, "level 3 address size fault"},
    {SIGSEGV, SEGV_MAPERR, "level 0 translation fault"},
    {SIGSEGV, SEGV_MAPERR, "level 1 translation fault"},
    {SIGSEGV, SEGV_MAPERR, "level 2 translation fault"},
    {SIGSEGV, SEGV_MAPERR, "level 3 translation fault"},
    {SIGSEGV, SEGV_ACCERR, "level 0 access flag fault"},
    {SIGSEGV, SEGV_ACCERR, "level 1 access flag fault"},
    {SIGSEGV, SEGV_ACCERR, "level 2 access flag fault"},
    {SIGSEGV, SEGV_ACCERR, "level 3 access flag fault"},
    {SIGSEGV, SEGV_ACCERR, "level 0 permission fault"},
    {SIGSEGV, SEGV_ACCERR, "level 1 permission fault"},
    {SIGSEGV, SEGV_ACCERR, "level 2 permission fault"},
    {SIGSEGV, SEGV_ACCERR, "level 3 permission fault"},
    {SIGBUS, BUS_OBJERR, "synchronous external abort"},
    {SIGSEGV, SEGV_MTESERR, "synchronous tag check fault"},
    {SIGKILL, SI_KERNEL, "unknown 18"},
    {SIGKILL, SI_KERNEL, "level -1 (translation table walk)"},
    {SIGKILL, SI_KERNEL, "level 0 (translation table walk)"},
    {SIGKILL, SI_KERNEL, "level 1 (translation table walk)"},
    {SIGKILL, SI_KERNEL, "level 2 (translation table walk)"},
    {SIGKILL, SI_KERNEL, "level 3 (translation table walk)"},
    {SIGBUS, BUS_OBJERR, "synchronous parity or ECC error"}, // Reserved when RAS is implemented
    {SIGKILL, SI_KERNEL, "unknown 25"},
    {SIGKILL, SI_KERNEL, "unknown 26"},
    {SIGKILL, SI_KERNEL, "level -1 synchronous parity error (translation table walk)"}, // Reserved
                                                                                        // when
                                                                                        // RAS is
    // implemented
    {SIGKILL, SI_KERNEL, "level 0 synchronous parity error (translation table walk)"}, // Reserved
                                                                                       // when RAS
                                                                                       // is
    // implemented
    {SIGKILL, SI_KERNEL, "level 1 synchronous parity error (translation table walk)"}, // Reserved
                                                                                       // when RAS
                                                                                       // is
    // implemented
    {SIGKILL, SI_KERNEL, "level 2 synchronous parity error (translation table walk)"}, // Reserved
                                                                                       // when RAS
                                                                                       // is
    // implemented
    {SIGKILL, SI_KERNEL, "level 3 synchronous parity error (translation table walk)"}, // Reserved
                                                                                       // when RAS
                                                                                       // is
    // implemented
    {SIGKILL, SI_KERNEL, "unknown 32"},
    {SIGBUS, BUS_ADRALN, "alignment fault"},
    {SIGKILL, SI_KERNEL, "unknown 34"},
    {SIGKILL, SI_KERNEL, "unknown 35"},
    {SIGKILL, SI_KERNEL, "unknown 36"},
    {SIGKILL, SI_KERNEL, "unknown 37"},
    {SIGKILL, SI_KERNEL, "unknown 38"},
    {SIGKILL, SI_KERNEL, "unknown 39"},
    {SIGKILL, SI_KERNEL, "unknown 40"},
    {SIGKILL, SI_KERNEL, "level -1 address size fault"},
    {SIGKILL, SI_KERNEL, "unknown 42"},
    {SIGSEGV, SEGV_MAPERR, "level -1 translation fault"},
    {SIGKILL, SI_KERNEL, "unknown 44"},
    {SIGKILL, SI_KERNEL, "unknown 45"},
    {SIGKILL, SI_KERNEL, "unknown 46"},
    {SIGKILL, SI_KERNEL, "unknown 47"},
    {SIGKILL, SI_KERNEL, "TLB conflict abort"},
    {SIGKILL, SI_KERNEL, "Unsupported atomic hardware update fault"},
    {SIGKILL, SI_KERNEL, "unknown 50"},
    {SIGKILL, SI_KERNEL, "unknown 51"},
    {SIGKILL, SI_KERNEL, "implementation fault (lockdown abort)"},
    {SIGBUS, BUS_OBJERR, "implementation fault (unsupported exclusive)"},
    {SIGKILL, SI_KERNEL, "unknown 54"},
    {SIGKILL, SI_KERNEL, "unknown 55"},
    {SIGKILL, SI_KERNEL, "unknown 56"},
    {SIGKILL, SI_KERNEL, "unknown 57"},
    {SIGKILL, SI_KERNEL, "unknown 58"},
    {SIGKILL, SI_KERNEL, "unknown 59"},
    {SIGKILL, SI_KERNEL, "unknown 60"},
    {SIGKILL, SI_KERNEL, "section domain fault"},
    {SIGKILL, SI_KERNEL, "page domain fault"},
    {SIGKILL, SI_KERNEL, "unknown 63"},
};

/************************全局变量******************************/
size_t irqstack[CONFIG_MAX_CPUS][8] = {0};
size_t fiqstack[CONFIG_MAX_CPUS][8] = {0};
size_t svcstack[CONFIG_MAX_CPUS][8] = {0};
size_t undefstack[CONFIG_MAX_CPUS][8] = {0};
size_t abortstack[CONFIG_MAX_CPUS][8] = {0};
extern unsigned char tmp_stack[PAGE_SIZE * CONFIG_MAX_CPUS] __attribute__((aligned(PAGE_SIZE)));

/************************函数实现******************************/

static inline const struct fault_info *esr_to_fault_info(unsigned long esr)
{
    return fault_info + (esr & ESR_ELx_FSC);
}

/* 检查用户态上下文是否被破坏 */
static inline bool user_context_valid(struct arch_context *context)
{
    if (!user_access_check((void *)context->sp, sizeof(context->sp), UACCESS_RW))
        return false;

    if (!user_access_check((void *)context->elr, sizeof(context->elr), UACCESS_R))
        return false;

    return true;
}

/**
 * @brief
 *    设置该上下文类型是否是系统调用上下文
 * @param[in] context 上下文
 * @retval 无
 */
void set_context_type(struct arch_context *context, int type)
{
    context->type = type;
    context->ori_x0 = context->regs[0];
}

/**
 * @brief
 *    上下文show
 * @param[in] context 上下文
 * @retval 无
 */
#define BREAKINST_ARM 0xd4200020

static void save_exce_context(pcb_t pcb, struct arch_context *context)
{
    memcpy(&pcb->exception_context, context, sizeof(pcb->exception_context));
}

void do_exception(struct arch_context *context)
{
    TASK_ID task = ttosGetRunningTask();
    const struct fault_info *info = esr_to_fault_info(context->esr);
    pcb_t pcb = NULL;

    if (GET_EL(context->cpsr) == MODE_EL0 && task && task->ppcb)
    {
        pcb = (pcb_t)task->ppcb;
        save_exce_context(pcb, context);
        TTOS_TaskEnterKernelHook(task);
    }

    /* 识别break poing exception */
    if (kernel_access_check((void *)(context->elr), 4, UACCESS_R))
    {
        uint32_t break_instr = *(uint32_t *)(context->elr);

        /* 检测断点异常 */
        if (BREAKINST_ARM == break_instr || ESR_ELx_EC(context->esr) == ESR_ELx_EC_SOFTSTP_LOW ||
            ESR_ELx_EC(context->esr) == ESR_ELx_EC_BREAKPT_LOW ||
            ESR_ELx_EC(context->esr) == ESR_ELx_EC_WATCHPT_LOW ||
            ESR_ELx_EC(context->esr) == ESR_ELx_EC_BRK64)

        {
            ksiginfo_t siginfo;

            siginfo.si_addr = (void *)read_far_el1();
            siginfo.si_code = 0x0004;
            if (pcb != NULL)
            {
                if (pcb->group_leader->ptrace & PT_SINGLESTEP)
                {
                    /* 取消所有断点 */
                    ptrace_cancel_bpt(pcb);
                }

                /* 向当前线程发送SIGTRAP信号 */
                kernel_signal_kill(task->tid, TO_THREAD, SIGTRAP, siginfo.si_code, &siginfo);

                /* 恢复上下文会做信号检测 */
                set_context_type(&pcb->exception_context, EXCEPTION_CONTEXT);
                restore_context(&pcb->exception_context);
            }
        }
    }

    if (GET_EL(context->cpsr) == MODE_EL0 && task && task->ppcb)
    {
        if ((ESR_ELx_IL & context->esr) && ESR_ELx_EC(context->esr) == 0)
        {
            pid_t pid = get_process_pid((pcb_t)task->ppcb);
            kernel_signal_kill(pid, TO_PROCESS, SIGILL, SI_KERNEL, NULL);
            set_context_type(context, EXCEPTION_CONTEXT);
            restore_context(context);
        }
    }

    if (GET_EL(context->cpsr) == MODE_EL1)
    {
        KLOG_EMERG("================Kernel Exception================");

        KLOG_EMERG("Case by: %s ESR: %p EC:0x%x Fault Address: %p(0x%" PRIx64 ")",
                   esr_get_class_string(context->esr), (void *)context->esr,
                   ESR_ELx_EC(context->esr), (void *)read_far_el1(),
                   mm_kernel_v2p((virt_addr_t)read_far_el1()));

        KLOG_EMERG("Reason: %s", info->name);
        KLOG_EMERG("RegMap:");
        KLOG_EMERG("========================================");
        KLOG_EMERG("cpacr     :%p", (void *)context->cpacr);
        KLOG_EMERG("vector    :%p", (void *)context->vector);
        KLOG_EMERG("cpsr      :%p", (void *)context->cpsr);
        KLOG_EMERG("esr       :%p", (void *)context->esr);
        KLOG_EMERG("sp        :%p", (void *)context->sp);

        size_t symsize;
        const struct symtab_item *sym = allsyms_findbyvalue((void *)context->elr, &symsize);

        if (sym)
        {
            KLOG_EMERG("elr       :%p (%s + %p)", (void *)context->elr, sym->sym_name,
                       (void *)(context->elr - (u64)sym->sym_value));
        }
        else
        {
            KLOG_EMERG("elr       :%p", (void *)context->elr);
        }

        for (int i = 0; i < 31; i++)
        {
            switch (i)
            {
            case 30:
            {
                size_t symsize;
                const struct symtab_item *sym =
                    allsyms_findbyvalue((void *)context->regs[i], &symsize);
                KLOG_EMERG("x%d(lr)   %s:%p (%s + %p)", i, i / 10 ? "" : " ",
                           (void *)context->regs[i], sym->sym_name,
                           (void *)(context->regs[i] - (u64)sym->sym_value));
            }

            break;
            case 29:
                KLOG_EMERG("x%d(fp)   %s:%p", i, i / 10 ? "" : " ", (void *)context->regs[i]);
                break;
            default:
                KLOG_EMERG("x%d       %s:%p", i, i / 10 ? "" : " ", (void *)context->regs[i]);
                break;
            }
        }
        KLOG_EMERG("========================================");

        if (kernel_access_check(task, sizeof(*task), UACCESS_R))
        {
            KLOG_EMERG("Exception Task: %s(%d) on CPU[%d]", task->objCore.objName,
                       kernel_access_check(task->ppcb, sizeof(struct T_TTOS_ProcessControlBlock *),
                                           UACCESS_R) &&
                               !(task->ppcb)
                           ? get_process_pid((pcb_t)task->ppcb)
                           : -1,
                       cpuid_get());

            /* 检测上下文是否在临时栈中，如果是栈溢出，则会使用临时栈来保存上下文
             */
            if ((unsigned long)context >= (unsigned long)tmp_stack &&
                (unsigned long)context <= ((unsigned long)tmp_stack + sizeof(tmp_stack)))
            {
                KLOG_EMERG("Stack OverFlow");
                KLOG_EMERG("OverFlow sp:%p", (void *)context->err_sp);
                context->sp = context->err_sp;
                KLOG_EMERG("Kernel Stack: %p-%p", task->stackStart, task->kernelStackTop);
            }
        }
        backtrace_r("exception", context->regs[29]);
    }
    else
    {
        KLOG_EMERG("================User Exception================");

        KLOG_EMERG("Case by: %s ESR: %p EC:0x%x Fault Address: %p(0x%" PRIx64 ")",
                   esr_get_class_string(context->esr), (void *)context->esr,
                   ESR_ELx_EC(context->esr), (void *)read_far_el1(),
                   mm_kernel_v2p((virt_addr_t)read_far_el1()));

        KLOG_EMERG("Reason: %s", info->name);
        KLOG_EMERG("RegMap:");
        KLOG_EMERG("========================================");
        KLOG_EMERG("cpacr     :%p", (void *)context->cpacr);
        KLOG_EMERG("vector    :%p", (void *)context->vector);
        KLOG_EMERG("cpsr      :%p", (void *)context->cpsr);
        KLOG_EMERG("esr       :%p", (void *)context->esr);
        KLOG_EMERG("sp        :%p", (void *)context->sp);
        KLOG_EMERG("elr       :%p", (void *)context->elr);
        for (int i = 0; i < 31; i++)
        {
            switch (i)
            {
            case 30:
            {
                KLOG_EMERG("x%d(lr)   %s:%p", i, i / 10 ? "" : " ", (void *)context->regs[i]);
            }

            break;
            case 29:
                KLOG_EMERG("x%d(fp)   %s:%p", i, i / 10 ? "" : " ", (void *)context->regs[i]);
                break;
            default:
                KLOG_EMERG("x%d       %s:%p", i, i / 10 ? "" : " ", (void *)context->regs[i]);
                break;
            }
        }
        KLOG_EMERG("========================================");
    }

    if (task)
    {
        if (GET_EL(context->cpsr) == MODE_EL0 && task->ppcb && info)
        {
            pcb_t pcb = (pcb_t)task->ppcb;
            pid_t pid = get_process_pid((pcb_t)task->ppcb);

            kernel_signal_kill(pid, TO_PROCESS, info->sig, info->code, NULL);
            set_context_type(context, EXCEPTION_CONTEXT);

            /* 避免返回到用户态的上下文信息被破坏 */
            if (user_context_valid(context))
            {
                restore_context(context);
            }
        }

        TTOS_SuspendTask(task);
    }
    while (1)
        ;
}

/**
 * @brief
 *    中断处理程序
 * @param[in] context 中断上下文
 * @retval 无
 */
void do_irq(arch_int_context_t *context)
{
    s32 ret;
    u32 from_cpu;
    u32 irq = 0;
    s32 cpuid = 0;

    TASK_ID task = ttosGetRunningTask();
    pcb_t pcb = NULL;

    if (GET_EL(context->cpsr) == MODE_EL0 && task && task->ppcb)
    {
        pcb = (pcb_t)task->ppcb;
        save_exce_context(pcb, context);
        TTOS_TaskEnterKernelHook(task);
    }

    cpuid = cpuid_get();

    ret = ttos_pic_irq_ack(&from_cpu, &irq);
    if (ret == 0)
    {
        // KLOG_I("irq happend irq:%d from cpu:%d", irq, from_cpu);
        TRACING_EVENT_ENTER(isr, irq, from_cpu);

        ttosDisableScheduleLevel[cpuid]++;
        intNestLevel[cpuid]++;

        ttos_pic_irq_handle(irq);

        ttos_pic_irq_eoi(irq, from_cpu);

        intNestLevel[cpuid]--;
        ttosDisableScheduleLevel[cpuid]--;

        TRACING_EVENT_EXIT(isr, irq);
    }

    ttosSchedule();

    set_context_type(context, IRQ_CONTEXT);
    restore_context(context);

    while (1)
        ;
}

/**
 * @brief
 *    data abort处理程序
 * @param[in] context 异常上下文
 * @retval 无
 */
void do_data_abort(arch_exception_context_t *context)
{
    do_exception(context);
}

/**
 * @brief
 *    syscall处理程序
 * @param[in] context 系统调用上下文
 * @retval 无
 */
void do_syscall(arch_exception_context_t *context)
{
    long long ret = 0;
    int syscall_num = 0;

    TASK_ID task = ttosGetRunningTask();
    pcb_t pcb = NULL;

    if (GET_EL(context->cpsr) == MODE_EL0 && task && task->ppcb)
    {
        pcb = (pcb_t)task->ppcb;
        save_exce_context(pcb, context);
        TTOS_TaskEnterKernelHook(task);
    }

    syscall_num = context->regs[8];

    set_context_type(context, SYSCALL_CONTEXT);

    arch_cpu_int_enable();

    if (is_extent_syscall_num(syscall_num))
    {
        syscall_num -= CONFIG_EXTENT_SYSCALL_NUM_START;
        if (0 == syscall_num)
        {
            ret = syscall_extent_table[syscall_num]((long)context, 0, 0, 0, 0, 0);
        }
        else
        {
            ret = syscall_extent_table[syscall_num](context->regs[0], context->regs[1],
                                                    context->regs[2], context->regs[3],
                                                    context->regs[4], context->regs[5]);
        }

        context->regs[0] = ret;
        restore_context(context);
    }

    if ((syscall_num >= CONFIG_SYSCALL_NUM))
    {
        KLOG_I("syscall num %d great than:%d\n", syscall_num, CONFIG_SYSCALL_NUM - 1);
    }
    else
    {
        if (syscall_num == __NR_rt_sigreturn)
        {
            /* 信号hander调用完毕的返回系统调用 */
            rt_sigreturn(context);
        }
        else if (syscall_table[syscall_num])
        {
            TRACING_EVENT_ENTER(syscall, syscall_num, syscall_getname(syscall_num),
                                context->regs[0], context->regs[1], context->regs[2],
                                context->regs[3], context->regs[4], context->regs[5]);

            ret = syscall_table[syscall_num](context->regs[0], context->regs[1], context->regs[2],
                                             context->regs[3], context->regs[4], context->regs[5]);

            context->regs[0] = ret;
            // KLOG_D ("[%d]SysCall: %s(%d) ret: %d(%s)",
            //         ttosProcessSelf () == NULL ? -1
            //             : get_process_pid (ttosProcessSelf ()),
            //         syscall_getname (syscall_num), syscall_num, ret,
            //         strerror (-ret));
            TRACING_EVENT_EXIT(syscall, syscall_getname(syscall_num), context->regs[0]);
        }
        else
        {
            KLOG_E("syscall_table[%d] is NULL\n", syscall_num);
        }
    }

    restore_context(context);
}
#if 0
bool stack_overflow (void)
{
    unsigned long sp_val = 0;

    asm volatile ("mov %0, sp" : "=r"(sp_val)::"memory");

    T_TTOS_TaskControlBlock *task = ttosGetCurrentCpuRunningTask ();

    /* 暂且认为只差128字节就要溢出也算溢出 */
    if (sp_val < (unsigned long)task->stackBottom + 128)
    {
        KLOG_E ("stack_overflow happend, overflow sp:%p!!!", sp_val);
        KLOG_E ("task:%s stack %p---%p !!!", task->objCore.objName,
                task->stackBottom, task->kernelStackTop);
        return true;
    }
    else
    {
        return false;
    }
}
#endif
