/**
 * @file    arch/arm/arch_signal.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * ac006b61 2024-07-02 移除一级ttos目录
 * 4dc228c0 2024-06-07 处理进程退出
 * 1f357380 2024-06-06 signal bug fix
 * 4bb423b7 2024-05-30 1.添加进程组 2.支持信号发送到进程组
 * 3823374e 2024-05-23 添加signal功能模块
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include <cache.h>
#include <context.h>
#include <errno.h>
#include <process_signal.h>
#include <ptrace.h>
#include <sigcontext.h>
#include <signal.h>
#include <syscall.h>
#include <ttos.h>
#include <ttosProcess.h>
#include <uaccess.h>

#undef KLOG_TAG
#define KLOG_TAG "arch_signal"
#include <klog.h>

//#define SIGNAL_DEBUG_INFO

extern void sigreturn_code(void);
int arch_valid_user_regs(struct arch_context *regs);
void signal_del_running(int signo);

void sys_exit_group(int flag);
T_TTOS_TaskControlBlock *current_task(void);
void thread_signal_mask_get(pcb_t pcb, process_sigset_t *oset);
int arch_valid_user_regs(struct arch_context *regs);
void restore_raw_context(arch_exception_context_t *context);

static int restore_sigframe(struct arch_context *context, struct signal_ucontext __user *sf)
{
    process_sigset_t set;
    int ret = 0;

    copy_from_user(&set, &sf->frame.uc_sigmask, sizeof(set));

    set_current_blocked(&set);

    context->r0 = sf->frame.uc_mcontext.arm_r0;
    context->r1 = sf->frame.uc_mcontext.arm_r1;
    context->r2 = sf->frame.uc_mcontext.arm_r2;
    context->r3 = sf->frame.uc_mcontext.arm_r3;
    context->r4 = sf->frame.uc_mcontext.arm_r4;
    context->r5 = sf->frame.uc_mcontext.arm_r5;
    context->r6 = sf->frame.uc_mcontext.arm_r6;
    context->r7 = sf->frame.uc_mcontext.arm_r7;
    context->r8 = sf->frame.uc_mcontext.arm_r8;
    context->r9 = sf->frame.uc_mcontext.arm_r9;
    context->sl = sf->frame.uc_mcontext.arm_r10;
    context->fp = sf->frame.uc_mcontext.arm_fp;
    context->ip = sf->frame.uc_mcontext.arm_ip;
    context->sp = sf->frame.uc_mcontext.arm_sp;
    context->lr = sf->frame.uc_mcontext.arm_lr;
    context->pc = sf->frame.uc_mcontext.arm_pc;
    context->cpsr = sf->frame.uc_mcontext.arm_cpsr;

    // ret = !arch_valid_user_regs(context);

#ifdef CONFIG_IWMMXT
    if (err == 0)
        err |= restore_iwmmxt_context(&aux);
#endif

#ifdef _HARD_FLOAT_
    /* 恢复浮点上下文 */
    memcpy(&context->fpContext, &sf->frame.uc_regspace, sizeof(context->fpContext));
#endif

#ifdef SIGNAL_DEBUG_INFO
    KLOG_EMERG("pcb:%p after signal_ucontext_restore:", ttosProcessSelf());
    int i = 0;
    unsigned int *reg = (unsigned int *)context;
    for (i = 0; i < 17; i++)
    {
        KLOG_EMERG("r%d:%p", i, reg[i]);
    }
#endif

    return ret;
}

static inline void __user *get_sigframe(struct ksignal *ksignal, struct arch_context *context,
                                        int framesize)
{
    unsigned long sp = sigsp(context->sp, ksignal);
    void __user *frame;

    /*
     * ATPCS B01 mandates 8-byte alignment
     */
    frame = (void __user *)((sp - framesize) & ~7);

    /*
     * Check that we can actually write to the signal frame.
     */
    if (!user_access_check(frame, framesize, UACCESS_W))
        frame = NULL;

    return frame;
}

static int setup_return(struct arch_context *context, struct ksignal *ksig,
                        struct signal_ucontext __user *frame)
{
    unsigned long retcode;
    unsigned int thumb = 0;
    unsigned long handler = (unsigned long)ksig->ka.__sa_handler.sa_handler;
    unsigned long cpsr = context->cpsr & ~(PSR_f | PSR_E_BIT);

    cpsr |= PSR_ENDSTATE;

    cpsr = (cpsr & ~MODE_MASK) | USR_MODE;

    /*
     * The LSB of the handler determines if we're going to
     * be using THUMB or ARM mode for this signal handler.
     */
    thumb = handler & 1;

    /*
     * Clear the If-Then Thumb-2 execution state.  ARM spec
     * requires this to be all 000s in ARM mode.  Snapdragon
     * S4/Krait misbehaves on a Thumb=>ARM signal transition
     * without this.
     *
     * We must do this whenever we are running on a Thumb-2
     * capable CPU, which includes ARMv6T2.  However, we elect
     * to always do this to simplify the code; this field is
     * marked UNK/SBZP for older architectures.
     */
    cpsr &= ~PSR_IT_MASK;

    /* 根据handler的地址判断handler是使用arm态运行还是使用thumb态运行 */
    if (thumb)
    {
        cpsr |= PSR_T_BIT;
    }
    else
    {
        cpsr &= ~PSR_T_BIT;
    }

    /* 用户自定义了信号的返回函数 */
    if (ksig->ka.sa_flags & SA_RESTORER)
    {
        /* 获取用户自定义的信号的返回函数 */
        retcode = (unsigned long)ksig->ka.sa_restorer;
    }
    else
    {
        /* 用户未定义信号的返回函数，则使用默认的信号返回函数 */
        memcpy(frame->sigreturn, &sigreturn_code, sizeof(frame->sigreturn));
        retcode = (unsigned long)&frame->sigreturn;

        /*
         * Ensure that the instruction cache sees
         * the return code written onto the stack.
         */
        cache_text_update((unsigned long)retcode, sizeof(frame->sigreturn));
    }

    context->r0 = ksig->sig;
    context->sp = (unsigned long)frame;
    context->lr = retcode;
    context->pc = handler;
    context->cpsr = cpsr;

    return 0;
}

static int setup_sigframe(ucontext_t __user *sf, struct arch_context *context,
                          process_sigset_t *set)
{
    unsigned int uc_regspace_size = 0;
    unsigned int fp_context_size = 0;
    unsigned int size = 0;

    sf->uc_mcontext.arm_r0 = context->r0;
    sf->uc_mcontext.arm_r1 = context->r1;
    sf->uc_mcontext.arm_r2 = context->r2;
    sf->uc_mcontext.arm_r3 = context->r3;
    sf->uc_mcontext.arm_r4 = context->r4;
    sf->uc_mcontext.arm_r5 = context->r5;
    sf->uc_mcontext.arm_r6 = context->r6;
    sf->uc_mcontext.arm_r7 = context->r7;
    sf->uc_mcontext.arm_r8 = context->r8;
    sf->uc_mcontext.arm_r9 = context->r9;
    sf->uc_mcontext.arm_r10 = context->sl;
    sf->uc_mcontext.arm_fp = context->fp;
    sf->uc_mcontext.arm_ip = context->ip;
    sf->uc_mcontext.arm_sp = context->sp;
    sf->uc_mcontext.arm_lr = context->lr;
    sf->uc_mcontext.arm_pc = context->pc;
    sf->uc_mcontext.arm_cpsr = context->cpsr;

    sf->uc_mcontext.fault_address = 0;
    sf->uc_mcontext.trap_no = 0;
    sf->uc_mcontext.error_code = 0;

    sf->uc_mcontext.oldmask = set->sig[0];

    memcpy(&sf->uc_sigmask, set, sizeof(sf->uc_sigmask));

#ifdef CONFIG_IWMMXT
    if (err == 0)
        err |= preserve_iwmmxt_context(&aux->iwmmxt);
#endif

#ifdef _HARD_FLOAT_

    /* 拷贝浮点上下文 */
    uc_regspace_size = sizeof(sf->uc_regspace);
    fp_context_size = sizeof(context->fpContext);
    size = (uc_regspace_size > fp_context_size) ? fp_context_size : uc_regspace_size;
    memcpy(&sf->uc_regspace, &context->fpContext, size);

#endif

#ifdef SIGNAL_DEBUG_INFO

    KLOG_EMERG("pcb:%p before signal_ucontext_save:", ttosProcessSelf());
    int i = 0;
    unsigned int *reg = (unsigned int *)context;
    for (i = 0; i < 17; i++)
    {
        KLOG_EMERG("r%d:%p", i, reg[i]);
    }

#endif

    return 0;
}

static int setup_frame(struct ksignal *ksignal, process_sigset_t *set, struct arch_context *context)
{
    struct signal_ucontext __user *frame = get_sigframe(ksignal, context, sizeof(*frame));
    int err = 0;

    if (!frame)
    {
        return 1;
    }

    /*
     * Set uc.uc_flags to a value which sc.trap_no would never have.
     */
    frame->frame.uc_flags = 0x5ac3c35a;
    setup_sigframe(&frame->frame, context, set);
    setup_return(context, ksignal, frame);

    return err;
}

static int setup_rt_frame(struct ksignal *ksignal, process_sigset_t *set,
                          struct arch_context *context)
{
    struct signal_ucontext __user *frame = get_sigframe(ksignal, context, sizeof(*frame));

    if (!frame)
    {
        KLOG_E("setup_rt_frame fail at file:%s line:%d", __FILE__, __LINE__);
        return 1;
    }

    memcpy(&frame->si, &ksignal->info, sizeof(frame->si));

    frame->frame.uc_link = NULL;
    frame->frame.uc_flags = 0;

    __save_altstack(&frame->frame.uc_stack, context->sp);

    setup_sigframe(&frame->frame, context, set);

    setup_return(context, ksignal, frame);

    context->r1 = (unsigned long)&frame->si;
    context->r2 = (unsigned long)&frame->frame;

    return 0;
}

static void handle_signal(struct ksignal *ksignal, struct arch_context *context)
{
    process_sigset_t *oldset = sigmask_to_save();

    if (ksignal->ka.sa_flags & SA_SIGINFO)
    {
        setup_rt_frame(ksignal, oldset, context);
    }
    else
    {
        setup_frame(ksignal, oldset, context);
    }

    context->cpsr = MODE32_USR;

    signal_delivered(ksignal, 0);

    TTOS_TaskEnterUserHook(ttosProcessSelf()->taskControlId);
    restore_raw_context(context);
}

bool is_thumb_mode(struct arch_context *context)
{
    return (context->cpsr & PSR_T_BIT);
}

void do_syscall_restart_check(struct arch_context *context, struct ksignal *ksignal)
{
    unsigned int retval = 0;
    unsigned int continue_addr = 0;
    unsigned int restart_addr = 0;

    /* 如果是系统调用,则需要考虑信号的重启功能 */
    if (SYSCALL_CONTEXT == context->type)
    {
        continue_addr = context->pc;
        restart_addr = continue_addr - (is_thumb_mode(context) ? 2 : 4);
        retval = context->r0;

        if (-ERESTARTNOHAND == retval)
        {
            context->r0 = -EINTR;
            context->pc = continue_addr;
        }
        else if (ksignal && -EINTR == retval && (ksignal->ka.sa_flags & SA_RESTART))
        {
            context->r0 = context->ori_r0;
            context->pc = restart_addr;
        }
    }
}

// #include <alltypes.h>
#include <time.h>
#include <time/ktime.h>
static struct timespec64 ts;

int rt_sigreturn(struct arch_context *context)
{
    struct signal_ucontext __user *frame;

    if (context->sp & 7)
        goto badframe;

    frame = (struct signal_ucontext __user *)context->sp;
    signal_del_running(frame->si.si_signo);

    if (!user_access_check(frame, sizeof(*frame), UACCESS_R))
        goto badframe;

    if (restore_sigframe(context, frame))
        goto badframe;

    if (restore_altstack(&frame->frame.uc_stack, context->sp))
        goto badframe;

    signal_forget_syscall(context);

    kernel_clock_gettime(CLOCK_MONOTONIC, &ts);

    // printk("kernel--> sig handle exit: %lld.%09lld\n", ts.tv_sec, ts.tv_nsec);

    return context->r0;

badframe:
    kernel_signal_kill(ttosProcessSelf()->taskControlId->tid, TO_THREAD, SIGSEGV, SI_KERNEL, NULL);

    return 0;
}

void setup_restart_syscall(struct arch_context *regs)
{
    regs->r7 = __NR_restart_syscall;
}

static void syscall_set_return_value(pcb_t pcb, struct arch_context *regs, int error, long val)
{
    regs->r0 = (long)error ? error : val;
}

int arch_do_signal(struct arch_context *regs)
{
    pcb_t pcb;
    int retval = 0;
    int restart = 0;
    struct ksignal ksig;
    bool is_in_syscall = false;
    unsigned long restart_addr = 0;
    unsigned long continue_addr = 0;

    pcb = ttosProcessSelf();
    if (!pcb)
    {
        return 0;
    }

    is_in_syscall = in_syscall(regs);

    /*
     * If we were from a system call, check for system call restarting...
     */
    if (is_in_syscall)
    {
        continue_addr = regs->pc;
        restart_addr = continue_addr - (is_thumb_mode(regs) ? 2 : 4);
        retval = regs->r0;

        /*
         * Avoid additional syscall restarting via ret_to_user.
         */
        signal_forget_syscall(regs);

        /*
         * Prepare for system call restart. We do this here so that a
         * debugger will see the already changed PC.
         */
        switch (retval)
        {
        case -ERESTART_RESTARTBLOCK:
            restart -= 2;
        case -ERESTARTNOHAND:
        case -ERESTARTSYS:
        case -ERESTARTNOINTR:
            restart++;
            regs->r0 = regs->ori_r0;
            regs->pc = restart_addr;
            break;
        }
    }

    /*
     * Get the signal to deliver. When running under ptrace, at this point
     * the debugger may change all of our registers.
     */
    if (get_signal(&ksig))
    {
        /* handler */
        if (unlikely(restart) && regs->pc == restart_addr)
        {
            if (retval == -ERESTARTNOHAND || retval == -ERESTART_RESTARTBLOCK ||
                (retval == -ERESTARTSYS && !(ksig.ka.sa_flags & SA_RESTART)))
            {
                regs->r0 = -EINTR;
                regs->pc = continue_addr;
            }
        }

        handle_signal(&ksig, regs);
    }
    else
    {
        /* no handler */
        restore_saved_sigmask();
        if (unlikely(restart) && regs->pc == restart_addr)
        {
            regs->r0 = -EINTR;
            regs->pc = continue_addr;
            return restart;
        }
    }

    return 0;
}
