/************************************************************************
 *              科东(广州)软件科技有限公司 版权所有
 *   Copyright (C) 2022 Intewell Inc. All Rights Reserved.
 ***********************************************************************/

/*
 * 修改历史：
 * 2024-02-29    毛玉泽，科东(广州)软件科技有限公司
 *               创建该文件。
 */

/*
 * @file： process_signal.h
 * @brief：
 *	    <li>信号相关函数声明及宏定义。</li>
 */
#ifndef _PROCESS_SIGNAL_H
#define _PROCESS_SIGNAL_H

/************************头文件********************************/
#include <list.h>
#include <signal.h>
#include <spinlock.h>
#include <sys/types.h>
#include <system/types.h>
#include <timer_event/timer.h>

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/************************宏定义********************************/
#define TO_THREAD 1
#define TO_PROCESS 2
#define TO_PGROUP 3

/* bit-flags */
#define SS_AUTODISARM (1U << 31) /* disable sas during sighandling */
/* mask for all SS_xxx flags */
#define SS_FLAG_BITS SS_AUTODISARM

#ifndef SA_NODEFER
#define SA_NODEFER 0x40000000
#endif

#define CONTEXT_NONE 0
#define SYSCALL_CONTEXT 1
#define IRQ_CONTEXT 2
#define EXCEPTION_CONTEXT 3

#define ERESTARTSYS 512
#define ERESTARTNOINTR 513
#define ERESTARTNOHAND 514        /* restart if no handler.. */
#define ENOIOCTLCMD 515           /* No ioctl command */
#define ERESTART_RESTARTBLOCK 516 /* restart by calling sys_restart_syscall */
#define EPROBE_DEFER 517          /* Driver requests probe retry */
#define EOPENSTALE 518            /* open found a stale dentry */
#define ENOPARAM 519              /* Parameter not supported */

#define SIGEV_SIGNAL 0
#define SIGEV_NONE 1
#define SIGEV_THREAD 2
#define SIGEV_THREAD_ID 4

#define SA_ONESHOT SA_RESETHAND

#ifndef SIGRTMIN
#define SIGRTMIN 32
#endif

#ifndef SIGRTMAX
#define SIGRTMAX (_NSIG - 1)
#endif

#define SS_ONSTACK 1
#define SS_DISABLE 2

/* bit-flags */
#define SS_AUTODISARM (1U << 31) /* disable sas during sighandling */
/* mask for all SS_xxx flags */
#define SS_FLAG_BITS SS_AUTODISARM

#define sigmask(sig) (1UL << ((sig)-1))

#define __user
/************************类型定义******************************/
typedef struct T_TTOS_ProcessControlBlock *pcb_t;

struct tty_struct;

union sigval {
    int sival_int;   /* Integer signal value */
    void *sival_ptr; /* Pointer signal value */
};

typedef struct siginfo
{
#ifdef __SI_SWAP_ERRNO_CODE
    int si_signo, si_code, si_errno;
#else
    int si_signo, si_errno, si_code;
#endif
    union {
        char __pad[128 - 2 * sizeof(int) - sizeof(long)];
        struct
        {
            union {
                struct
                {
                    pid_t si_pid;
                    uid_t si_uid;
                } __piduid;
                struct
                {
                    int si_timerid;
                    int si_overrun;
                } __timer;
            } __first;
            union {
                union sigval si_value;
                struct
                {
                    int si_status;
                    clock_t si_utime, si_stime;
                } __sigchld;
            } __second;
        } __si_common;
        struct
        {
            void *si_addr;
            short si_addr_lsb;
            union {
                struct
                {
                    void *si_lower;
                    void *si_upper;
                } __addr_bnd;
                unsigned si_pkey;
            } __first;
        } __sigfault;
        struct
        {
            long si_band;
            int si_fd;
        } __sigpoll;
        struct
        {
            void *si_call_addr;
            int si_syscall;
            unsigned si_arch;
        } __sigsys;
    } __si_fields;
} ksiginfo_t;

typedef struct siginfo siginfo_t;

struct sigaction
{
    union {
        void (*sa_handler)(int);
        void (*sa_sigaction)(int, siginfo_t *, void *);
    } __sa_handler;

    int sa_flags;
    void (*sa_restorer)(void);
    sigset_t sa_mask;
};

struct ksignal
{
    struct sigaction ka;
    siginfo_t info;
    int sig;
};

struct ksigevent
{
    union sigval sigev_value;
    int sigev_signo;
    int sigev_notify;
    int sigev_tid;
};

typedef struct T_TTOS_TaskControlBlock_Struct T_TTOS_TaskControlBlock;

typedef void (*process_sighandler_t)(int);

typedef void (*process_sigaction_t)(int signo, siginfo_t *info, void *context);

#ifdef CONFIG_OS_LP64
#define _PROCESS_NSIG_BPW 64
#else
#define _PROCESS_NSIG_BPW 32
#endif

#define SIGNAL_MAX 64

#ifndef ALIGN
#define ALIGN(x, a) (((x) + ((a)-1)) & ~((a)-1))
#endif

#define SIG_BLOCK 0
#define SIG_UNBLOCK 1
#define SIG_SETMASK 2

#define SI_ASYNCNL (-60)
#define SI_TKILL (-6)
#define SI_SIGIO (-5)
#define SI_ASYNCIO (-4)
#define SI_MESGQ (-3)
#define SI_TIMER (-2)
#define SI_QUEUE (-1)
#define SI_USER 0
#define SI_KERNEL 128

#define SIG_HOLD ((void (*)(int))2)

#define FPE_INTDIV 1
#define FPE_INTOVF 2
#define FPE_FLTDIV 3
#define FPE_FLTOVF 4
#define FPE_FLTUND 5
#define FPE_FLTRES 6
#define FPE_FLTINV 7
#define FPE_FLTSUB 8

#define ILL_ILLOPC 1
#define ILL_ILLOPN 2
#define ILL_ILLADR 3
#define ILL_ILLTRP 4
#define ILL_PRVOPC 5
#define ILL_PRVREG 6
#define ILL_COPROC 7
#define ILL_BADSTK 8

#define SEGV_MAPERR 1
#define SEGV_ACCERR 2
#define SEGV_BNDERR 3
#define SEGV_PKUERR 4
#define SEGV_MTEAERR 8
#define SEGV_MTESERR 9

#define BUS_ADRALN 1
#define BUS_ADRERR 2
#define BUS_OBJERR 3
#define BUS_MCEERR_AR 4
#define BUS_MCEERR_AO 5

#define CLD_EXITED 1
#define CLD_KILLED 2
#define CLD_DUMPED 3
#define CLD_TRAPPED 4
#define CLD_STOPPED 5
#define CLD_CONTINUED 6

#define _USIGNAL_SIGMASK(signo) (1u << ((signo)-1))
#define PROCESS_SIG_NO_IGN_SET                                                                     \
    (_USIGNAL_SIGMASK(SIGCONT) | _USIGNAL_SIGMASK(SIGSTOP) | _USIGNAL_SIGMASK(SIGKILL))
#define PROCESS_SIG_IGNORE_SET                                                                     \
    (_USIGNAL_SIGMASK(SIGCHLD) | _USIGNAL_SIGMASK(SIGURG) | _USIGNAL_SIGMASK(SIGWINCH) |           \
     _USIGNAL_SIGMASK(SIGCONT))
#define PROCESS_SIG_JOBCTL_SET                                                                     \
    (_USIGNAL_SIGMASK(SIGCONT) | _USIGNAL_SIGMASK(SIGSTOP) | _USIGNAL_SIGMASK(SIGTSTP) |           \
     _USIGNAL_SIGMASK(SIGTTIN) | _USIGNAL_SIGMASK(SIGTTOU))
#define PROCESS_SIG_STOP_SET                                                                       \
    (_USIGNAL_SIGMASK(SIGSTOP) | _USIGNAL_SIGMASK(SIGTSTP) | _USIGNAL_SIGMASK(SIGTTIN) |           \
     _USIGNAL_SIGMASK(SIGTTOU))

#define PROCESS_SIG_COREDUMP_SET                                                                   \
    (_USIGNAL_SIGMASK(SIGQUIT) | _USIGNAL_SIGMASK(SIGILL) | _USIGNAL_SIGMASK(SIGTRAP) |            \
     _USIGNAL_SIGMASK(SIGABRT) | _USIGNAL_SIGMASK(SIGFPE) | _USIGNAL_SIGMASK(SIGSEGV) |            \
     _USIGNAL_SIGMASK(SIGBUS) | _USIGNAL_SIGMASK(SIGSYS) | _USIGNAL_SIGMASK(SIGXCPU) |             \
     _USIGNAL_SIGMASK(SIGXFSZ))

#define _PROCESS_NSIG_WORDS (ALIGN(SIGNAL_MAX, _PROCESS_NSIG_BPW) / _PROCESS_NSIG_BPW)

typedef struct
{
    unsigned long sig[_PROCESS_NSIG_WORDS];
} process_sigset_t;

#if SIGNAL_MAX <= 64
#define process_sigmask(signo) ((process_sigset_t){.sig = {[0] = ((long)(1u << ((signo)-1)))}})
#define process_sigset_init(mask) ((process_sigset_t){.sig = {[0] = (long)(mask)}})
#endif /* _PROCESS_NSIG <= 64 */

#define _USIGNAL_SIGMASK(signo) (1u << ((signo)-1))
#define PROCESS_SIG_ACT_DFL ((process_sighandler_t)0)
#define PROCESS_SIG_ACT_IGN ((process_sighandler_t)1)

struct signal_wqueue
{
    unsigned int flag;
    struct list_head waiting_list;
    ttos_spinlock_t spinlock;
};
typedef struct signal_wqueue signal_wqueue_t;

struct process_notify
{
    void (*notify)(signal_wqueue_t *signalfd_queue, int signo);
    signal_wqueue_t *signalfd_queue;
    struct list_head list_node;
};

/* Structure to transport application-defined values with signals.  */
typedef struct sigevent
{
    union sigval sigev_value;
    int sigev_signo;
    int sigev_notify;

    void (*_function)(union sigval); /* Function to start.  */
    void *_attribute;                /* Thread attributes.  */
} sigevent_t;

#define si_pid __si_fields.__si_common.__first.__piduid.si_pid
#define si_uid __si_fields.__si_common.__first.__piduid.si_uid
#define si_status __si_fields.__si_common.__second.__sigchld.si_status
#define si_utime __si_fields.__si_common.__second.__sigchld.si_utime
#define si_stime __si_fields.__si_common.__second.__sigchld.si_stime
#define si_value __si_fields.__si_common.__second.si_value
#define si_addr __si_fields.__sigfault.si_addr
#define si_addr_lsb __si_fields.__sigfault.si_addr_lsb
#define si_lower __si_fields.__sigfault.__first.__addr_bnd.si_lower
#define si_upper __si_fields.__sigfault.__first.__addr_bnd.si_upper
#define si_pkey __si_fields.__sigfault.__first.si_pkey
#define si_band __si_fields.__sigpoll.si_band
#define si_fd __si_fields.__sigpoll.si_fd
#define si_timerid __si_fields.__si_common.__first.__timer.si_timerid
#define si_overrun __si_fields.__si_common.__first.__timer.si_overrun
#define si_ptr si_value.sival_ptr
#define si_int si_value.sival_int
#define si_call_addr __si_fields.__sigsys.si_call_addr
#define si_syscall __si_fields.__sigsys.si_syscall
#define si_arch __si_fields.__sigsys.si_arch

typedef struct process_siginfo
{
    struct list_head node;
    ksiginfo_t ksiginfo;
} process_siginfo_t;

typedef struct process_sigqueue
{
    struct list_head siginfo_list;
    process_sigset_t sigset_pending;
    process_sigset_t sigset_running;
} * process_sigqueue_t;

struct ttos_signal
{
    ttos_spinlock_t siglock;
    bool need_restore_sigset_mask;
    struct timer_event real_timer;

    struct process_sigqueue sig_queue;

    /* 控制终端 */
    void *ctty;

    /* 不生成 SIGCHLD 信号，当子进程停止或暂停时 */
    process_sigset_t nocldstop_set;

    /* 当子进程死亡时不产生僵尸进程 */
    process_sigset_t nocldwait_set;

    /* 旧的前台进程组 */
    pid_t tty_old_pgrp;

    /* 控制终端 */
    struct tty_struct *tty;
};

struct sighand_struct
{
    ttos_spinlock_t siglock;
    struct sigaction sig_action[_NSIG];
};

typedef enum
{
    PROCESS_SIG_MASK_CMD_BLOCK,
    PROCESS_SIG_MASK_CMD_UNBLOCK,
    PROCESS_SIG_MASK_CMD_SET_MASK,
    __PROCESS_SIG_MASK_CMD_WATERMARK
} process_sig_mask_cmd_t;

/* 对应musl struct k_sigaction */
struct process_sigaction
{
    union {
        /* 当sa_flags中没有设置SA_SIGINFO标志时，使用该handler */
        void (*_sa_handler)(int);

        /* 当sa_flags中设置了SA_SIGINFO标志时，使用该handler */
        void (*_sa_sigaction)(int, siginfo_t *, void *);
    } __sa_handler;

    unsigned long sa_flags;

    void (*sa_restorer)(void);

    /* 在信号处理函数执行期间要阻塞的信号集 */
    process_sigset_t sa_mask;
};

/************************接口声明******************************/
int process_thread_signal_mask(pcb_t pcb, process_sig_mask_cmd_t how,
                               const process_sigset_t *sigset, process_sigset_t *oset);
int process_signal_init(struct ttos_signal *sig);
void signal_copy(pcb_t child_process, pcb_t parent_process);

int kernel_signal_kill(pid_t pid, int to, long signo, long code, ksiginfo_t *kinfo);
int kernel_signal_kill_with_worker(pid_t pid, int to, long signo, long code, ksiginfo_t *kinfo);

int fork_signal(unsigned long clone_flags, pcb_t child);
bool task_signal_available(void);
bool pending_signal_exist(void);
int signal_reset(pcb_t pcb);
unsigned long sigsp(unsigned long sp, struct ksignal *ksignal);
void sas_ss_reset(pcb_t pcb);
void __save_altstack(stack_t __user *uss, unsigned long sp);
void set_current_blocked(process_sigset_t *newset);
void _sigorsets(process_sigset_t *dset, const process_sigset_t *set0, const process_sigset_t *set1);
void _sigaddset(process_sigset_t *set, int _sig);
int restore_altstack(const stack_t *uss, unsigned long sp);
process_sigset_t *sigmask_to_save(void);
int sas_ss_flags(unsigned long sp);
int rt_sigreturn(struct arch_context *regs);
int do_sigaltstack(const stack_t *ss, stack_t *oss, unsigned long sp, size_t min_ss_size);
void signal_delivered(struct ksignal *ksig, int stepping);
bool task_sigpending(void);
bool signal_available(void);
void do_syscall_restart_check(struct arch_context *context, struct ksignal *ksignal);
void signal_forget_syscall(struct arch_context *context);
void sigsuspend(const sigset_t *set);
bool in_syscall(struct arch_context *context);
bool get_signal(struct ksignal *ksig);
void restore_saved_sigmask(void);
int arch_do_signal(struct arch_context *regs);
void signal_set_running(int signo);
int __on_sig_stack(unsigned long sp);
int on_sig_stack(unsigned long sp);
int copy_siginfo_to_user(siginfo_t __user *to, const siginfo_t *from);
#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* _PROCESS_SIGNAL_H */
