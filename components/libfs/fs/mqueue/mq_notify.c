#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <fs/fs.h>
#include <fs/kpoll.h>
#include <mqueue.h>
#include <poll.h>
#include <stdarg.h>
#include <stdbool.h>
#include <stdio.h>
#include <sys/stat.h>
#include <ttos.h>
#include <ttosInterHal.h>

#include "../inode/inode.h"
#include "mqueue.h"

T_TTOS_TaskControlBlock *current_task (void);

/*
 * @brief: 
 *    消息通知处理函数,当消息到达时发送信号通知任务。
 * @param[in]: argument: 消息队列描述符。
 * @return: 
 *     无。
 */
static void mq_notify_handler(void *argument)
{
    struct mq_control *mqc = (struct mq_control *)argument;
	
	/* 发送信号通知任务有消息到达 */
    if (mqc->notify_sigevent.sigev_notify == SIGEV_SIGNAL)
		kernel_signal_kill(mqc->notify_tid, TO_THREAD, mqc->notify_sigevent.sigev_signo, SI_KERNEL, NULL);

	/* 通知发送完成后移除注册的信号，消息队列可再次被注册信号 */
	ttosSetMessageNotify(mqc->msgqID, NULL, NULL);
	
	/* 清空信号通知任务ID */
	mqc->notify_tid = 0;
}

int vfs_mq_notify (mqd_t mqdes, struct sigevent *notification)
{
    struct file *filep;
	struct mqueue_inode_s *msgq;
    int ret = 0;

    ret = fs_getfilep(mqdes, &filep);
    if (ret < 0)
    {
      return ret;
    }

	msgq = filep->f_inode->i_private;

	if(!INODE_IS_MQUEUE(filep->f_inode))
    {
        return -EBADFD;
    }

    /* 注册/注销消息通知信号 */
	if (notification)
	{	  		
  		/* 检查任务是否已经注册通知信号 */
  		if (0 != msgq->ttos_msgq->mqc.notify_tid)
  		{
        	return -EBUSY;	
  		}
  		
		ttosSetMessageNotify((T_TTOS_MsgqControlBlock * )msgq->ttos_msgq, mq_notify_handler, &msgq->ttos_msgq->mqc);
		msgq->ttos_msgq->mqc.notify_tid = current_task()->tid;
  		msgq->ttos_msgq->mqc.notify_sigevent = *notification;
	}
	else
	{
		ttosSetMessageNotify((T_TTOS_MsgqControlBlock * )msgq->ttos_msgq, NULL, NULL);
		msgq->ttos_msgq->mqc.notify_tid = 0;
        memset(&msgq->ttos_msgq->mqc.notify_sigevent, 0, sizeof(struct sigevent));
	}
    return ret;
}
