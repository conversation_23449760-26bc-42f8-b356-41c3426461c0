
#include <errno.h>
#include <time.h>
#include <time/ktime.h>
#include <time/posix_timer.h>
#include <ttos.h>
#include <ttosInterTask.inl>
#include <ttosProcess.h>
#include <ttos_time.h>

#define PROCESS_CLOCK make_process_cpuclock(0, CPUCLOCK_SCHED)
#define THREAD_CLOCK make_thread_cpuclock(0, CPUCLOCK_SCHED)

void time_event_sleep(const struct timespec64 *rqtp, struct timespec64 *rmtp);
extern int signal_in_pending(pcb_t pcb, long signo, int flag);
extern int signal_in_running(pcb_t pcb, long signo);

static u64 realtime_ns_offset;

u64 kernel_realtime_current_ns_get(void)
{
    return timer_timestamp();
}

static void kernel_realtime_offset_set(struct timespec64 *tp)
{
    u64 now_ns = kernel_realtime_current_ns_get();

    realtime_ns_offset = tp->tv_nsec + tp->tv_sec * NANOSECOND_PER_SECOND - now_ns;
}

static void kernel_realtime_offset_correction(u64 *correction_ns)
{
    *correction_ns += realtime_ns_offset;
}

static struct timespec64 kernel_reatime_to_monotonic(const struct timespec64 *tp)
{
    u64 now_ns = tp->tv_nsec + tp->tv_sec * NANOSECOND_PER_SECOND - realtime_ns_offset;
    struct timespec64 ret;
    ret.tv_sec = now_ns / NANOSECOND_PER_SECOND;
    ret.tv_nsec = now_ns % NANOSECOND_PER_SECOND;

    return ret;
}

static int posix_get_hrtimer_res(const clockid_t which_clock, struct timespec64 *tp)
{
    tp->tv_sec = 0;
    tp->tv_nsec = 1;
    return 0;
}

void kernel_realtime64_get(const clockid_t which_clock, struct timespec64 *tp)
{
    u64 ns = kernel_realtime_current_ns_get();

    if (which_clock == CLOCK_REALTIME)
    {
        kernel_realtime_offset_correction(&ns);
    }

    tp->tv_sec = ns / NANOSECOND_PER_SECOND;
    tp->tv_nsec = ns % NANOSECOND_PER_SECOND;
}

static int posix_get_realtime_timespec(const clockid_t which_clock, struct timespec64 *tp)
{
    kernel_realtime64_get(which_clock, tp);

    return 0;
}

static ktime_t posix_get_realtime_ktime(const clockid_t which_clock)
{
    return kernel_realtime_current_ns_get();
}

static int posix_clock_realtime_set(const clockid_t which_clock, const struct timespec64 *tp)
{
    kernel_realtime_offset_set((struct timespec64 *)tp);

    return 0;
}

static int common_nsleep(const clockid_t which_clock, int flags, const struct timespec64 *tm)
{
    int ret = 0;
    if (flags == TIMER_ABSTIME)
    {
        /* 如果是实时时钟 需要去掉实时时钟的偏移值 */
        if (which_clock == CLOCK_REALTIME)
        {
            struct timespec64 tp;

            tp = kernel_reatime_to_monotonic(tm);
            time_event_sleep(&tp, NULL);
        }
        else
        {
            time_event_sleep(tm, NULL);
        }

        if (TTOS_TIMEOUT == ttosGetRunningTask()->wait.returnCode)
        {
            ret = 0;
        }
        else
        {
            ret = -ttos_ret_to_errno(ttosGetRunningTask()->wait.returnCode);
        }
    }
    else
    {
        u64 expiry_ns = tm->tv_nsec + tm->tv_sec * NANOSECOND_PER_SECOND;
        if (expiry_ns)
        {
            /* 因为是相对时间 所以取monotonic 时间 */
            struct timespec64 ts;
            kernel_realtime64_get(CLOCK_MONOTONIC, &ts);
            ts = clock_timespec_add64(&ts, tm);
            time_event_sleep(&ts, NULL);
        }

        if (TTOS_TIMEOUT == ttosGetRunningTask()->wait.returnCode)
        {
            ret = 0;
        }
        else
        {
            ret = -ttos_ret_to_errno(ttosGetRunningTask()->wait.returnCode);
        }
    }

    ttosGetRunningTask()->wait.returnCode = 0;

    return ret;
}

static int common_timer_create(struct k_itimer *new_timer)
{
    return 0;
}

void posix_timer_handle(struct timer_event *timr)
{
    struct k_itimer *timer = (struct k_itimer *)timr->priv;

    if (timer->pcb->group_exit_status.is_terminated)
    {
        timer_event_stop(timr);
        return;
    }

    if (timer->sigev_notify == SIGEV_THREAD_ID)
    {
        if ((signal_in_pending(timer->pcb, timer->siginfo.si_signo, TO_THREAD) == 0) ||
            (signal_in_running(timer->pcb->group_leader, timer->siginfo.si_signo) == 0))
        {
            timer->overcount++;
        }
        kernel_signal_kill(timer->pcb->taskControlId->tid, TO_THREAD, timer->siginfo.si_signo,
                           timer->siginfo.si_code, &timer->siginfo);
    }
    else if (timer->sigev_notify == SIGEV_NONE)
    {
        /* 不需要发signal通知 */
    }
    else
    {
        if (signal_in_pending(timer->pcb->group_leader, timer->siginfo.si_signo, TO_PROCESS) == 0 ||
            (signal_in_running(timer->pcb->group_leader, timer->siginfo.si_signo) == 0))
        {
            timer->overcount++;
        }
        pid_t pid = get_process_pid(timer->pcb->group_leader);
        kernel_signal_kill(pid, TO_PROCESS, timer->siginfo.si_signo, timer->siginfo.si_code,
                           &timer->siginfo);
    }
}

static int common_timer_set(struct k_itimer *ktimr, int flags, struct itimerspec64 *new_setting,
                            struct itimerspec64 *old_setting)
{
    s64 interval_ns = timespec64_to_ns(&new_setting->it_interval);
    s64 duration_ns = timespec64_to_ns(&new_setting->it_value);
    s64 expiry_tstamp;
    struct itimerspec64 new = *new_setting;

    *old_setting = ktimr->timer_val;
    ktimr->timer_val = new;

    if (new.it_value.tv_sec == 0 && new.it_value.tv_nsec == 0)
    {
        if (ktimr->timer_id.active_state)
            timer_event_stop(&ktimr->timer_id);

        return 0;
    }

    if (ktimr->timer_id.active_state)
    {
        expiry_tstamp = timer_event_expiry_time(&ktimr->timer_id) - timer_timestamp();

        if (expiry_tstamp < 0)
        {
            ktimr->timer_id.handler(&ktimr->timer_id);
            expiry_tstamp += timespec64_to_ns(&old_setting->it_interval);
        }

        old_setting->it_value.tv_sec = expiry_tstamp / (u64)NSEC_PER_SEC;
        old_setting->it_value.tv_nsec = expiry_tstamp % (u64)NSEC_PER_SEC;

        if (ktimr->kclock)
        {
            while (ktimr->kclock->timer_try_to_cancel(ktimr))
                ;
        }
    }

    INIT_TIMER_PRRIODIC_EVENT(&ktimr->timer_id, posix_timer_handle, ktimr,
                              interval_ns ? TTOS_WAIT_FOREVER : 1, interval_ns);

    timer_event_start2(&ktimr->timer_id, duration_ns, NULL);

    return 0;
}

static void common_timer_get(struct k_itimer *timr, struct itimerspec64 *cur_setting)
{
    s64 expiry_tstamp;

    if (!timr->timer_id.active_state)
    {
        memset(cur_setting, 0, sizeof(*cur_setting));
    }
    else
    {
        expiry_tstamp = timer_event_expiry_time(&timr->timer_id) - timer_timestamp();
        cur_setting->it_interval = timr->timer_val.it_interval;
        cur_setting->it_value.tv_sec = expiry_tstamp / (u64)NSEC_PER_SEC;
        cur_setting->it_value.tv_nsec = expiry_tstamp % (u64)NSEC_PER_SEC;
    }
}

static int common_timer_del(struct k_itimer *timr)
{
    timer_event_stop(&timr->timer_id);

    return 0;
}

static int common_hrtimer_try_to_cancel(struct k_itimer *timr)
{
    if (timr->timer_id.active_state)
        timer_event_stop(&timr->timer_id);

    return 0;
}

static void task_enum(pcb_t pcb, void *param)
{
    struct timespec64 *tp = param;

    tp->tv_sec += pcb->utime.tv_sec;
    tp->tv_nsec += pcb->utime.tv_nsec;

    while (tp->tv_nsec > NSEC_PER_SEC)
    {
        tp->tv_sec++;
        tp->tv_nsec -= NSEC_PER_SEC;
    }
}

static int posix_get_process_timespec(const clockid_t which_clock, struct timespec64 *tp)
{
    int pid = CPUCLOCK_PID(which_clock);
    pcb_t pcb;

    if (pid == 0)
    {
        pcb = ttosProcessSelf();
    }
    else
    {
        TASK_ID task = task_get_by_tid(pid);
        if (task == NULL)
        {
            return -EINVAL;
        }
        pcb = task->ppcb;
    }

    if (pcb == NULL)
    {
        return -EINVAL;
    }

    tp->tv_sec = 0;
    tp->tv_nsec = 0;
    foreach_task_group(pcb, task_enum, tp);
    return 0;
}

static int process_get_process_timespec(const clockid_t which_clock, struct timespec64 *tp)
{
    return posix_get_process_timespec(PROCESS_CLOCK, tp);
}

static int process_get_thread_timespec(const clockid_t which_clock, struct timespec64 *tp)
{
    return posix_get_process_timespec(THREAD_CLOCK, tp);
}

static const struct k_clock clock_realtime = {
    .clock_getres = posix_get_hrtimer_res,
    .clock_get_timespec = posix_get_realtime_timespec,
    .clock_get_ktime = posix_get_realtime_ktime,
    .clock_set = posix_clock_realtime_set,
    // .clock_adj              = posix_clock_realtime_adj,
    .nsleep = common_nsleep,
    .timer_create = common_timer_create,
    .timer_set = common_timer_set,
    .timer_get = common_timer_get,
    .timer_del = common_timer_del,
    // .timer_rearm            = common_hrtimer_rearm,
    // .timer_forward          = common_hrtimer_forward,
    // .timer_remaining        = common_hrtimer_remaining,
    .timer_try_to_cancel = common_hrtimer_try_to_cancel,
    // .timer_wait_running     = common_timer_wait_running,
    // .timer_arm              = common_hrtimer_arm,
};

static const struct k_clock clock_monotonic = {
    .clock_getres = posix_get_hrtimer_res,
    .clock_get_timespec = posix_get_realtime_timespec,
    .clock_get_ktime = posix_get_realtime_ktime,
    // .clock_set              = posix_clock_realtime_set,
    // .clock_adj              = posix_clock_realtime_adj,
    .nsleep = common_nsleep,
    .timer_create = common_timer_create,
    .timer_set = common_timer_set,
    .timer_get = common_timer_get,
    .timer_del = common_timer_del,
    // .timer_rearm            = common_hrtimer_rearm,
    // .timer_forward          = common_hrtimer_forward,
    // .timer_remaining        = common_hrtimer_remaining,
    .timer_try_to_cancel = common_hrtimer_try_to_cancel,
    // .timer_wait_running     = common_timer_wait_running,
    // .timer_arm              = common_hrtimer_arm,
};

static const struct k_clock clock_process = {
    .clock_getres = posix_get_hrtimer_res,
    .clock_get_timespec = process_get_process_timespec,
};

static const struct k_clock clock_thread = {
    .clock_getres = posix_get_hrtimer_res,
    .clock_get_timespec = process_get_thread_timespec,
};

static const struct k_clock clock_posix_cpu = {
    .clock_getres = posix_get_hrtimer_res,
    .clock_get_timespec = posix_get_process_timespec,
};

static const struct k_clock *const posix_clocks[] = {
    [CLOCK_REALTIME] = &clock_realtime,
    [CLOCK_MONOTONIC] = &clock_monotonic,
    [CLOCK_PROCESS_CPUTIME_ID] = &clock_process,
    [CLOCK_THREAD_CPUTIME_ID] = &clock_thread,
    [CLOCK_MONOTONIC_RAW] = &clock_monotonic,
    [CLOCK_REALTIME_COARSE] = &clock_realtime,
    [CLOCK_MONOTONIC_COARSE] = &clock_monotonic,
    [CLOCK_BOOTTIME] = &clock_monotonic,
    [CLOCK_REALTIME_ALARM] = &clock_realtime,
    [CLOCK_BOOTTIME_ALARM] = &clock_monotonic,
    [CLOCK_TAI] = NULL,
};

const struct k_clock *clockid_to_kclock(const clockid_t id)
{
    if (id < 0)
    {
        return (id & CLOCKFD_MASK) == CLOCKFD ? NULL : &clock_posix_cpu;
    }

    if (id >= (sizeof(posix_clocks) / sizeof(posix_clocks[0])))
        return NULL;

    return posix_clocks[id];
}
