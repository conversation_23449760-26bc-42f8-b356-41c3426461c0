/**
 * @file kill.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 向进程发送信号
 * @version 3.0.0
 * @date 2024-11-14
 *
 * @ingroup syscall
 *
 * @since 3.0.0
 *
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 *
 */

#include "syscall_internal.h"
#include <errno.h>
#include <fs/fs.h>
#include <pgroup.h>
#include <process_signal.h>
#include <signal.h>
#include <stdio.h>
#include <tasklist_lock.h>
#include <ttosBase.h>
#include <ttosProcess.h>
#include <uaccess.h>

/**
 * @brief 向进程发送信号
 *
 * 该函数实现了一个系统调用，用于向指定进程发送信号。
 *
 * @param[in] pid 目标进程ID：
 *                - >0：发送给指定进程
 *                - =0：发送给同进程组的所有进程
 *                - =-1：发送给所有可发送的进程
 *                - <-1：发送给进程组|pid|中的所有进程
 * @param[in] sig 要发送的信号
 * @return 成功时返回0，失败时返回负值错误码。
 * @retval 0 成功发送信号。
 * @retval -EINVAL sig无效。
 * @retval -ESRCH 目标进程或进程组不存在。
 * @retval -EPERM 没有权限发送信号。
 *
 * @note 1. 常用信号包括SIGTERM、SIGKILL等。
 *       2. 部分信号不能被忽略（如SIGKILL）。
 *       3. 权限检查基于进程的实际或有效用户ID。
 *       4. 进程可以向自己发送信号。
 */
static void process_signal_kill_to(pcb_t pcb, long sig)
{
    pid_t pid;
    if (pcb != NULL)
    {
        pid = get_process_pid(pcb);
        if (pid != 1)
        {
            kernel_signal_kill(pid, TO_PROCESS, sig, SI_USER, 0);
        }
    }
}

/**
 * @brief 系统调用实现：向进程发送信号。
 *
 * 该函数实现了一个系统调用，用于向指定进程发送信号。
 *
 * @param[in] pid 目标进程ID：
 *                - >0：发送给指定进程
 *                - =0：发送给同进程组的所有进程
 *                - =-1：发送给所有可发送的进程
 *                - <-1：发送给进程组|pid|中的所有进程
 * @param[in] sig 要发送的信号
 * @return 成功时返回0，失败时返回负值错误码。
 * @retval 0 成功发送信号。
 * @retval -EINVAL sig无效。
 * @retval -ESRCH 目标进程或进程组不存在。
 * @retval -EPERM 没有权限发送信号。
 *
 * @note 1. 常用信号包括SIGTERM、SIGKILL等。
 *       2. 部分信号不能被忽略（如SIGKILL）。
 *       3. 权限检查基于进程的实际或有效用户ID。
 *       4. 进程可以向自己发送信号。
 */
DEFINE_SYSCALL(kill, (pid_t pid, int sig))
{
    long flags = 0;
    int ret = 0;
    pcb_t pcb;

    KLOG_D("process:%s[%d] send signo:%d to pid:%d", ttosProcessSelf()->cmd_name,
           get_process_pid(ttosProcessSelf()), sig, pid);

    /* If pid is greater than 0, sig shall be sent to the process whose process ID is equal to pid
     */
    if (pid > 0)
    {
        ret = kernel_signal_kill(pid, TO_PROCESS, sig, SI_USER, 0);
    }
    else if (pid < -1 || pid == 0)
    {
        pid_t pgid = 0;
        pgroup_t group;

        KLOG_D("syscall kill pid:%d", pid);

        if (pid == 0)
        {
            /* If pid is 0, sig shall be sent to all processes (excluding an unspecified set of
               system processes) whose process group ID is equal to the process group ID of the
               sender, and for which the process has permission to send a signal. */

            pgid = process_pgid_get_byprocess(ttosProcessSelf());
        }
        else
        {
            /**
             * sig shall be sent to all processes (excluding an unspecified set
             * of system processes) whose process group ID is equal to the absolute
             * value of pid, and for which the process has permission to send a signal.
             */
            pgid = -pid;
        }

        ret = kernel_signal_kill(pgid, TO_PGROUP, sig, SI_USER, 0);
    }
    else if (pid == -1)
    {
        /* @KEEP_COMMENT: 禁止调度器 */
        tasklist_lock();

        process_foreach((void (*)(pcb_t, void *))process_signal_kill_to, (void *)(long)sig);

        /* @KEEP_COMMENT: 重新使能调度 */
        tasklist_unlock();
        ret = 0;
    }

    return ret;
}

#ifdef CONFIG_SHELL
#include <shell.h>

int kill_cmd(int argc, const char *argv[])
{
    if (argc != 2)
    {
        printf("argc should be 2");
    }
    pid_t pid = atoi(argv[1]);
    if (pid == 0)
    {
        printf("Can not kill kernel");
    }
    return SYSCALL_FUNC(kill)(pid, 0);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_MAIN) |
                     SHELL_CMD_DISABLE_RETURN,
                 kill, kill_cmd, kill process);
#endif /* CONFIG_SHELL */
