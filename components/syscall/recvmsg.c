/**
 * @file recvmsg.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 从套接字接收消息
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"
#include <errno.h>
#include <net/net.h>
#include <uaccess.h>
/**
 * @brief 系统调用实现：从套接字接收消息。
 *
 * 该函数实现了一个系统调用，用于从套接字接收消息及其控制信息。
 *
 * @param[in] sockfd 套接字文件描述符
 * @param[in,out] msg msghdr结构，包含：
 *                    - msg_name：发送方地址
 *                    - msg_iov：数据缓冲区数组
 *                    - msg_control：控制信息
 * @param[in] flags 接收标志：
 *                  - MSG_PEEK：查看数据
 *                  - MSG_WAITALL：等待所有数据
 *                  - MSG_DONTWAIT：非阻塞
 * @return 成功时返回接收的字节数，失败时返回负值错误码。
 * @retval >0 成功接收的字节数。
 * @retval 0 连接已关闭。
 * @retval -EBADF 无效的文件描述符。
 * @retval -EINVAL 参数无效。
 *
 * @note 1. 支持分散接收。
 *       2. 可获取控制信息。
 *       3. 可能阻塞。
 *       4. 线程安全。
 */
DEFINE_SYSCALL (recvmsg, (int fd, struct msghdr __user *msg, unsigned flags))
{
    ssize_t        recv_len = 0;

    if (NULL == msg)
    {
        return -EINVAL;
    }

    recv_len = vfs_recvmsg (fd, msg, flags);
}
