/**
 * @file sendmsg.c
 * <AUTHOR> (z<PERSON><PERSON><EMAIL>)
 * @brief 发送消息
 * @version 3.0.0
 * @date 2024-11-14
 *
 * @ingroup syscall
 *
 * @since 3.0.0
 *
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 *
 */

#include "syscall_internal.h"
#include <errno.h>
#include <net/net.h>
#include <string.h>
#include <uaccess.h>

/**
 * @brief 系统调用实现：发送消息。
 *
 * 该函数实现了一个系统调用，用于通过套接字发送消息。
 *
 * @param[in] fd 套接字文件描述符
 * @param[in] msg 消息结构体
 * @param[in] flags 发送标志：
 *                  - MSG_OOB：发送带外数据
 *                  - MSG_DONTROUTE：不路由
 *                  - MSG_DONTWAIT：非阻塞
 *                  - MSG_NOSIGNAL：不产生SIGPIPE信号
 * @return 成功时返回发送的字节数，失败时返回负值错误码。
 * @retval >0 成功发送的字节数。
 * @retval -EBADF 无效的文件描述符。
 * @retval -EINVAL 参数无效。
 * @retval -EFAULT 内存访问错误。
 * @retval -EAGAIN 资源暂时不可用。
 *
 * @note 1. 支持多缓冲区。
 *       2. 支持控制信息。
 *       3. 支持多种标志。
 *       4. 可能阻塞。
 */
DEFINE_SYSCALL(sendmsg, (int fd, struct msghdr __user *msg, unsigned flags))
{
    ssize_t send_len;

    if (NULL == msg)
    {
        return -EINVAL;
    }

    send_len = vfs_sendmsg(fd, msg, flags);

    return send_len;
}
