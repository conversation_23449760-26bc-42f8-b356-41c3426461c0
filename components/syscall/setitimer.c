/**
 * @file setitimer.c
 * <AUTHOR> (zhang<PERSON><EMAIL>)
 * @brief 设置间隔定时器
 * @version 3.0.0
 * @date 2024-11-14
 *
 * @ingroup syscall
 *
 * @since 3.0.0
 *
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 *
 */

#include "syscall_internal.h"
#include <assert.h>
#include <time.h>
#include <time/ktime.h>
#include <ttosProcess.h>
#include <uaccess.h>

/**
 * @brief 定时器事件处理函数
 *
 * 当定时器到期时，发送SIGALRM信号给进程。
 *
 * @param[in] ev 定时器事件结构体
 */
static void alarm_handler(struct timer_event *ev)
{
    pid_t pid = get_process_pid((pcb_t)(ev->priv));
    kernel_signal_kill(pid, TO_PROCESS, SIGALRM, SI_TIMER, NULL);
}

/**
 * @brief 系统调用实现：设置进程的间隔定时器。
 *
 * 该函数实现了一个系统调用，用于设置和管理进程的间隔定时器。
 * 定时器可以在指定的时间间隔发送SIGALRM（ITIMER_REAL）、
 * SIGVTALRM（ITIMER_VIRTUAL）或SIGPROF（ITIMER_PROF）信号。
 *
 * @param[in] which 定时器类型：
 *                - ITIMER_REAL: 实时定时器，计算实际流逝的时间
 *                - ITIMER_VIRTUAL: 虚拟定时器，只计算进程用户态时间
 *                - ITIMER_PROF: 性能分析定时器，计算用户态和内核态时间
 * @param[in] value 新的定时器值：
 *                    - it_interval: 定时器间隔时间
 *                    - it_value: 距离下次到期的时间
 * @param[out] ovalue 原定时器值：
 *                     - NULL: 不获取原值
 *                     - 非NULL: 返回原定时器值
 * @return 成功时返回0，失败时返回负值错误码。
 * @retval 0 操作成功。
 * @retval -EFAULT value或ovalue指向无效内存。
 * @retval -EINVAL which无效或定时器值无效。
 *
 * @note 1. 如果it_value为0，定时器将被禁用。
 *       2. 如果it_interval为0，定时器将只触发一次。
 *       3. ITIMER_VIRTUAL和ITIMER_PROF只在进程运行时计时。
 */
DEFINE_SYSCALL(setitimer,
               (int which, struct itimerval __user *value, struct itimerval __user *ovalue))
{
    struct itimerspec new_value;
    struct timer_event *timer;
    uint64_t tstamp = 0;
    long kvalue[4];

    pcb_t pcb = ttosProcessSelf();
    assert(pcb != NULL);

    if (which == ITIMER_REAL)
    {
        copy_from_user(kvalue, value, sizeof(kvalue));

        new_value.it_value.tv_sec = kvalue[2];
        new_value.it_value.tv_nsec = kvalue[3];

        timer = &(get_process_signal(pcb)->real_timer);

        if ((new_value.it_value.tv_nsec + new_value.it_value.tv_sec) == 0)
        {
            timer_event_stop(timer);
            return 0;
        }

        timer->priv = pcb;
        timer->handler = alarm_handler;

        if (timer->active_state)
        {
            tstamp = timer_timestamp();
            tstamp = (timer->expiry_tstamp - tstamp) / NSEC_PER_SEC;

            kvalue[2] = tstamp / NSEC_PER_SEC;
            kvalue[3] = tstamp % NSEC_PER_SEC;

            copy_to_user(ovalue, kvalue, sizeof(kvalue));
        }

        timer_event_start2(
            timer, NSEC_PER_SEC * new_value.it_value.tv_sec + new_value.it_value.tv_nsec, NULL);
    }

    return tstamp;
}