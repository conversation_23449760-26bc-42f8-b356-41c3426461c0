/**
 * @file sendto.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 发送数据到指定地址
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"
#include <fs/fs.h>
#include <errno.h>
#include <uaccess.h>
#include <sys/socket.h>
#include <net/net.h>

/**
 * @brief 系统调用实现：发送数据到指定地址。
 *
 * 该函数实现了一个系统调用，用于通过套接字发送数据到指定的目标地址。
 *
 * @param[in] fd 套接字文件描述符
 * @param[in] buf 要发送的数据缓冲区
 * @param[in] len 要发送的数据长度
 * @param[in] flags 发送标志：
 *                  - MSG_OOB：发送带外数据
 *                  - MSG_DONTROUTE：不路由
 *                  - MSG_DONTWAIT：非阻塞
 *                  - MSG_NOSIGNAL：不产生SIGPIPE信号
 * @param[in] addr 目标地址结构体
 * @param[in] addr_len 地址结构体长度
 * @return 成功时返回发送的字节数，失败时返回负值错误码。
 * @retval >0 成功发送的字节数。
 * @retval -EBADF 无效的文件描述符。
 * @retval -EINVAL 参数无效。
 * @retval -EFAULT 内存访问错误。
 * @retval -EAGAIN 资源暂时不可用。
 *
 * @note 1. 支持多种协议。
 *       2. 支持多种地址。
 *       3. 支持多种标志。
 *       4. 可能阻塞。
 */
DEFINE_SYSCALL (sendto, (int fd, void __user *buff, size_t len, unsigned flags,
                         struct sockaddr __user *addr, socklen_t addr_len))

{
    struct sockaddr *kaddr = NULL;
    void            *kbuff = NULL;
    int                ret;
    ssize_t       send_len;

    if (addr_len < 0)
    {
        return -EINVAL;
    }

    if (!user_access_check (buff, len, UACCESS_R))
    {
        return -EFAULT;
    }

    send_len = vfs_sendto (fd, buff, len, flags, addr, addr_len);

    return send_len;
}
