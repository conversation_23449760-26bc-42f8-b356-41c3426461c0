#include <stdint.h>

#include <errno.h>
#include <netinet/in.h>
#include <time/ktime.h>
#include <ttos.h>

#include <lwip/ip4_addr.h>
#include <lwip/stats.h>
#include <lwip/sys.h>

#define KLOG_LEVEL KLOG_ERR
#define KLOG_TAG "LWIP_OS"
#include <klog.h>

/* (1 / ticks_per_ms) */
#define TICKS_PER_MS_REV (1000 / TTOS_GetSysClkRate())

#define MSG_PTR_SIZE sizeof(void *)

static u32_t ticks_per_sec;

sys_mutex_t arch_protect_mutex = NULL;

u32_t get_ticks_from_ms(u32_t time_ms)
{
    return (u32_t)((u64_t)ticks_per_sec * (u64_t)time_ms / 1000) + 1;
}

void sys_init(void)
{
#if !SYS_ARCH_PROTECT_WITH_INTERRUTP
    // 创建临界保护互斥锁
    sys_mutex_new(&arch_protect_mutex);
#endif

    ticks_per_sec = TTOS_GetSysClkRate();
    // void_pointer_size = sizeof (void *);

    KLOG_D("lwip sys_init ok");
}

/**
 * @ingroup sys_mutex
 * Create a new mutex.
 * Note that mutexes are expected to not be taken recursively by the lwIP code,
 * so both implementation types (recursive or non-recursive) should work.
 * @param mutex pointer to the mutex to create
 * @return ERR_OK if successful, another err_t otherwise
 */
err_t sys_mutex_new(sys_mutex_t *mutex)
{
    T_TTOS_ReturnCode ret;

    if (NULL == mutex)
    {
        return ERR_ARG;
    }

    ret = TTOS_CreateMutex(1, 0, mutex);

    if (ret != TTOS_OK)
    {
        KLOG_E("Create mutex failed ret: %d", ret);
        return ERR_MEM;
    }

    return ERR_OK;
}

/**
 * @ingroup sys_mutex
 * Lock a mutex
 * @param mutex the mutex to lock
 */
void sys_mutex_lock(sys_mutex_t *mutex)
{
    T_TTOS_ReturnCode ret = TTOS_ObtainMutex(*mutex, TTOS_SEMA_WAIT_FOREVER);
    if (ret != TTOS_OK && ret != TTOS_TIMEOUT)
    {
        KLOG_E("Obtain mutex failed ret %d", ret);
    }
}

/**
 * @ingroup sys_mutex
 * Unlock a mutex
 * @param mutex the mutex to unlock
 */
void sys_mutex_unlock(sys_mutex_t *mutex)
{
    T_TTOS_ReturnCode ret = TTOS_ReleaseMutex(*mutex);
    if (ret != TTOS_OK && ret != TTOS_TIMEOUT)
    {
        KLOG_E("Release mutex failed ret %d", ret);
    }
}

/**
 * @ingroup sys_mutex
 * Delete a mutex
 * @param mutex the mutex to delete */
void sys_mutex_free(sys_mutex_t *mutex)
{
    TTOS_DeleteMutex(*mutex);
    *mutex = NULL;
}

/**
 * @ingroup sys_sem
 * Create a new semaphore
 * @param sem pointer to the semaphore to create
 * @param count initial count of the semaphore
 * @return ERR_OK if successful, another err_t otherwise
 */
err_t sys_sem_new(sys_sem_t *sem, u8_t count)
{
    T_TTOS_ReturnCode ret;

    if (NULL == sem)
    {
        return ERR_ARG;
    }

    ret = TTOS_CreateSemaEx(count, sem);
    if (TTOS_OK != ret)
    {
        KLOG_E("Create sem failed ret: %d", ret);
        return ERR_WOULDBLOCK;
    }

    return ERR_OK;
}

/**
 * @ingroup sys_sem
 * Wait for a semaphore for the specified timeout
 * @param sem the semaphore to wait for
 * @param timeout timeout in milliseconds to wait (0 = wait forever)
 * @return time (in milliseconds) waited for the semaphore
 *         or SYS_ARCH_TIMEOUT on timeout
 */
u32_t sys_arch_sem_wait(sys_sem_t *sem, u32_t timeout)
{
    T_TTOS_ReturnCode ret;
    T_UWORD ticks = TTOS_SEMA_WAIT_FOREVER;
    T_UDWORD ticksStart = 0;
    T_UDWORD ticksEnd = 0;
    u32_t millis_waited;

    if (0 != timeout)
    {
        ticks = get_ticks_from_ms(timeout);
        ticks = (ticks > 0) ? ticks : 1;
        ticks = (ticks == (T_UWORD)TTOS_SEMA_WAIT_FOREVER) ? ticks - 1 : ticks;
    }

    TTOS_GetSystemTicks(&ticksStart);

    ret = TTOS_ObtainSema(*sem, ticks);
    if (TTOS_SIGNAL_INTR == ret)
    {
        KLOG_D("wait sem interrupted ret: %d", ret);
        errno = ERR_RESTARTSYS;
        return SYS_ARCH_TIMEOUT;
    }
    else if (TTOS_OK != ret)
    {
        if (ticks != TTOS_SEMA_WAIT_FOREVER && ret != TTOS_TIMEOUT)
        {
            KLOG_D("wait sem failed ret:%d", ret);
        }
        errno = ERR_TIMEOUT;
        return SYS_ARCH_TIMEOUT;
    }

    TTOS_GetSystemTicks(&ticksEnd);

    millis_waited = (u32_t)((ticksEnd - ticksStart) * TICKS_PER_MS_REV);

    return (millis_waited == (u32_t)SYS_ARCH_TIMEOUT) ? millis_waited - 1 : millis_waited;
}

u32_t sys_arch_sem_wait_uninterruptable(sys_sem_t *sem, u32_t timeout)
{
    T_TTOS_ReturnCode ret;
    T_UWORD ticks = TTOS_SEMA_WAIT_FOREVER;
    T_UDWORD ticksStart = 0;
    T_UDWORD ticksEnd = 0;
    u32_t millis_waited;

    if (0 != timeout)
    {
        ticks = get_ticks_from_ms(timeout);
        ticks = (ticks > 0) ? ticks : 1;
        ticks = (ticks == (T_UWORD)TTOS_SEMA_WAIT_FOREVER) ? ticks - 1 : ticks;
    }

    TTOS_GetSystemTicks(&ticksStart);

    ret = TTOS_ObtainSemaUninterruptable(*sem, ticks);

    if (TTOS_OK != ret)
    {
        KLOG_D("wait sem failed ret:%d", ret);
        errno = ERR_TIMEOUT;
        return SYS_ARCH_TIMEOUT;
    }

    TTOS_GetSystemTicks(&ticksEnd);

    millis_waited = (u32_t)((ticksEnd - ticksStart) * TICKS_PER_MS_REV);

    return (millis_waited == (u32_t)SYS_ARCH_TIMEOUT) ? millis_waited - 1 : millis_waited;
}

/**
 * @ingroup sys_sem
 * Signals a semaphore
 * @param sem the semaphore to signal
 */
void sys_sem_signal(sys_sem_t *sem)
{
    T_TTOS_ReturnCode ret = TTOS_ReleaseSema(*sem);
    if (ret != TTOS_OK)
    {
        KLOG_E("Release sem failed ret %d", ret);
    }
}

/**
 * @ingroup sys_sem
 * Delete a semaphore
 * @param sem semaphore to delete
 */
void sys_sem_free(sys_sem_t *sem)
{
    TTOS_DeleteSema(*sem);
    *sem = NULL;
}

#if 1
/**
 * @ingroup sys_mbox
 * Create a new mbox of specified size
 * @param mbox pointer to the mbox to create
 * @param size (minimum) number of messages in this mbox
 * @return ERR_OK if successful, another err_t otherwise
 */
err_t sys_mbox_new(sys_mbox_t *mbox, int size)
{
    T_TTOS_ReturnCode ret;

    ret = TTOS_CreateMsgqEx(MSG_PTR_SIZE, (T_UWORD)size, mbox);
    if (ret != TTOS_OK)
    {
        KLOG_E("create mbox failed ret:%d", ret);
        return ERR_MEM;
    }

    return ERR_OK;
}

/**
 * @ingroup sys_mbox
 * Delete an mbox
 * @param mbox mbox to delete
 */
void sys_mbox_free(sys_mbox_t *mbox)
{
    if ((mbox != NULL) && (*mbox != NULL))
    {
        TTOS_DeleteMsgq(*mbox);
        *mbox = NULL;
    }
}

/**
 * @ingroup sys_mbox
 * Try to post a message to an mbox - may fail if full or ISR
 * @param mbox mbox to posts the message
 * @param msg message to post (ATTENTION: can be NULL)
 */
err_t sys_mbox_trypost(sys_mbox_t *mbox, void *msg)
{
    if (TTOS_SendMsgq(*mbox, &msg, MSG_PTR_SIZE, TTOS_NO_WAIT, 0) != TTOS_OK)
    {
        return ERR_WOULDBLOCK;
    }

    return ERR_OK;
}

err_t sys_mbox_trypost_fromisr(sys_mbox_t *q, void *msg)
{
    return sys_mbox_trypost(q, msg);
}

/**
 * @ingroup sys_mbox
 * Post a message to an mbox - may not fail
 * -> blocks if full, only used from tasks not from ISR
 * @param mbox mbox to posts the message
 * @param msg message to post (ATTENTION: can be NULL)
 */
void sys_mbox_post(sys_mbox_t *mbox, void *msg)
{
    if (TTOS_SendMsgq(*mbox, &msg, MSG_PTR_SIZE, TTOS_WAIT, TTOS_MSGQ_WAIT_FOREVER) != TTOS_OK)
    {
        KLOG_E("post mbox failed");
    }
}

u32_t sys_arch_mbox_tryfetch(sys_mbox_t *mbox, void **msg)
{
    T_UWORD size = MSG_PTR_SIZE;
    T_TTOS_ReturnCode ret;

    ret = TTOS_ReceiveMsgq(*mbox, (T_VOID *)msg, &size, TTOS_NO_WAIT, 0);
    if (TTOS_UNSATISFIED == ret)
    {
        return SYS_MBOX_EMPTY;
    }

    return 0;
}

/**
 * @ingroup sys_mbox
 * Wait for a new message to arrive in the mbox
 * @param mbox mbox to get a message from
 * @param msg pointer where the message is stored
 * @param timeout maximum time (in milliseconds) to wait for a message (0 = wait
 forever)
 * @return time (in milliseconds) waited for a message, may be 0 if not waited
           or SYS_ARCH_TIMEOUT on timeout
 *         The returned time has to be accurate to prevent timer jitter!
 */
u32_t sys_arch_mbox_fetch(sys_mbox_t *mbox, void **msg, u32_t timeout)
{
    T_UWORD ticks = TTOS_MSGQ_WAIT_FOREVER;
    T_TTOS_ReturnCode ret;
    T_UDWORD ticksStart = 0;
    T_UDWORD ticksEnd = 0;
    T_UWORD size = MSG_PTR_SIZE;
    u32_t millis_waited;

    if (0 != timeout)
    {
        ticks = get_ticks_from_ms(timeout);
        ticks = (ticks > 0) ? ticks : 1;
        ticks = (ticks == (T_UWORD)TTOS_MSGQ_WAIT_FOREVER) ? ticks - 1 : ticks;
    }

    TTOS_GetSystemTicks(&ticksStart);

    ret = TTOS_ReceiveMsgq(*mbox, (T_VOID *)msg, &size, TTOS_WAIT, ticks);
    if (TTOS_SIGNAL_INTR == ret)
    {
        errno = ERR_RESTARTSYS;
        return SYS_ARCH_TIMEOUT;
    }
    else if (TTOS_OK != ret)
    {
        if (ticks != TTOS_MSGQ_WAIT_FOREVER && ret != TTOS_TIMEOUT)
        {
            KLOG_E("wait mbox failed ret:%d", ret);
        }
        errno = ERR_TIMEOUT;
        return SYS_ARCH_TIMEOUT;
    }

    TTOS_GetSystemTicks(&ticksEnd);

    millis_waited = (u32_t)((ticksEnd - ticksStart) * TICKS_PER_MS_REV);

    return (millis_waited == (u32_t)SYS_ARCH_TIMEOUT) ? millis_waited - 1 : millis_waited;
}
#endif

/**
 * @ingroup sys_mutex
 * Check if a mutex is valid/allocated: return 1 for valid, 0 for invalid
 */
int sys_mutex_valid(sys_mutex_t *mutex)
{
    return sys_valid(mutex);
}

/**
 * @ingroup sys_mutex
 * Set a mutex invalid so that sys_mutex_valid returns 0
 */
void sys_mutex_set_invalid(sys_mutex_t *mutex)
{
    sys_set_invalid(mutex);
}

/**
 * @ingroup sys_sem
 * Returns 1 if the semaphore is valid, 0 if it is not valid.
 * When using pointers, a simple way is to check the pointer for != NULL.
 * When directly using OS structures, implementing this may be more complex.
 * This may also be a define, in which case the function is not prototyped.
 */
int sys_sem_valid(sys_sem_t *sem)
{
    return sys_valid(sem);
}

/**
 * @ingroup sys_sem
 * Invalidate a semaphore so that sys_sem_valid() returns 0.
 * ATTENTION: This does NOT mean that the semaphore shall be deallocated:
 * sys_sem_free() is always called before calling this function!
 * This may also be a define, in which case the function is not prototyped.
 */
void sys_sem_set_invalid(sys_sem_t *sem)
{
    sys_set_invalid(sem);
}

/**
 * @ingroup sys_mbox
 * Check if an mbox is valid/allocated: return 1 for valid, 0 for invalid
 */
int sys_mbox_valid(sys_mbox_t *mbox)
{
    return sys_valid(mbox);
}

/**
 * @ingroup sys_mbox
 * Set an mbox invalid so that sys_mbox_valid returns 0
 */
void sys_mbox_set_invalid(sys_mbox_t *mbox)
{
    sys_set_invalid(mbox);
}

/**
 * @ingroup sys_misc
 * The only thread function:
 * Creates a new thread
 * ATTENTION: although this function returns a value, it MUST NOT FAIL (ports
 * have to assert this!)
 * @param name human-readable name for the thread (used for debugging purposes)
 * @param thread thread-function
 * @param arg parameter passed to 'thread'
 * @param stacksize stack size in bytes for the new thread (may be ignored by
 * ports)
 * @param prio priority of the new thread (may be ignored by ports) */
sys_thread_t sys_thread_new(const char *name, lwip_thread_fn thread, void *arg, int stacksize,
                            int prio)
{
    sys_thread_t taskid;
    T_TTOS_ReturnCode ret;

    ret = TTOS_CreateTaskEx((T_UBYTE *)name, (T_UBYTE)prio, TRUE, TRUE, (T_TTOS_TaskRoutine)thread,
                            arg, (T_UWORD)stacksize, &taskid);

    if (TTOS_OK != ret)
    {
        KLOG_E("failed to create new task");
        return NULL;
    }

    /* 将LwIP主任务与网卡接收任务保持在同一CPU核上 */
#ifdef CONFIG_ETH_RX_TASK_BIND_CPU_CORE
#ifdef CONFIG_SMP
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(CONFIG_ETH_RX_TASK_AFFINITY, &cpuset);
    (void)TTOS_SetTaskAffinity(taskid, &cpuset);
#endif
#endif

    return taskid;
}

/**
 * @ingroup sys_time
 * Returns the current time in milliseconds,
 * may be the same as sys_jiffies or at least based on it.
 */
u32_t sys_now(void)
{
    uint32_t ms;
    struct timespec64 ts;

    kernel_clock_gettime(CLOCK_MONOTONIC, &ts);

    /* todoSysticks:需要分析返回值的类型是否足够 */
    ms = ts.tv_sec * 1000 + ts.tv_nsec / 1000000;

    return ms;
}

_Noreturn void ttos_lwip_abort()
{
    while (1)
    {
        TTOS_SuspendTask(TTOS_SELF_OBJECT_ID);
    }
}
