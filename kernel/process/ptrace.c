#include "ptrace/ptrace.h"
#include "tasklist_lock.h"
#include <assert.h>
#include <elf.h>
#include <errno.h>
#include <stdint.h>
#include <sys/ptrace.h>
#include <sys/uio.h>
#include <ttos.h>
#include <ttosProcess.h>
#include <ttosUtils.inl>
#include <uaccess.h>

#undef KLOG_LEVEL
#define KLOG_LEVEL KLOG_DEBUG

#undef KLOG_TAG
#define KLOG_TAG "ptrace"
#include <klog.h>

#define PTRACE_GETHBPREGS 29
#define PTRACE_SETHBPREGS 30

#ifdef __arm__
#define GENERAL_REGS_SIZE (18 * 4)
#elif defined(__aarch64__)
/*
struct user_pt_regs {
        __u64		regs[31];
        __u64		sp;
        __u64		pc;
        __u64		pstate;
};
*/
#define GENERAL_REGS_SIZE (34 * 8)
#endif

static inline int put_user(void *from, void *to)
{
    int copy_size = 0;

    copy_size = copy_to_user(to, from, sizeof(unsigned long));

    return copy_size;
}

/*
 * ptrace a task: make the debugger its new parent and
 * move it to the ptrace list.
 *
 * Must be called with the tasklist lock write-held.
 */
void __ptrace_link(pcb_t child, pcb_t new_parent)
{
    // if (!list_empty(&child->ptrace_list))
    // 	BUG();
    if (child->parent == new_parent)
        return;
    tasklist_lock();
    // list_add(&child->ptrace_list, &child->parent->ptrace_children);
    // REMOVE_LINKS(child);

    /* 从子进程中移除 */
    if (child->parent)
    {
        if (child->parent->first_child == child)
        {
            child->parent->first_child = child->sibling;
        }
        else
        {
            pcb_t node = child->parent->first_child;
            while (node->sibling != NULL)
            {
                if (node->sibling == child)
                {
                    node->sibling = child->sibling;
                    break;
                }

                node = node->sibling;
            }
        }
        child->sibling = NULL; // 确保移除后 sibling 指针被清空
        child->parent = NULL;
    }

    tasklist_unlock();

    /* 添加新的父子进程关系 */
    child->parent = new_parent;
    child->sibling = new_parent->first_child;
    new_parent->first_child = child;
    // SET_LINKS(child);
    // child->ptracer = new_parent;
}

/*
 * unptrace a task: move it back to its original parent and
 * remove it from the ptrace list.
 *
 * Must be called with the tasklist lock write-held.
 */
void __ptrace_unlink(pcb_t child)
{
    // if (!child->ptrace)
    // 	BUG();
    // child->ptrace = 0;
    // if (list_empty(&child->ptrace_list))
    // 	return;
    // list_del_init(&child->ptrace_list);
    // REMOVE_LINKS(child);
    // child->parent = child->real_parent;
    // SET_LINKS(child);
}

void wait_task_inactive(pcb_t pcb)
{
    // process_signal_kill(pcb, SIGSTOP, SI_USER, 0);
#if 0
	pcb_t thread;
	unsigned int flag;

	list_for_each_entry(thread, &pcb->thread_group, sibling_node)
	{
		unsigned int stat;

		/**
		 * Note: all stopped thread will be resumed
		 */
		ttosDisableTaskDispatchWithLock();
		ttos_int_lock(flag);
		stat = thread->taskControlId->state;

		ttos_int_unlock(flag);
		ttosEnableTaskDispatchWithLock();
		TTOS_SuspendTask(thread->taskControlId);
		
	}
#endif
}

/*
 * Check that we have indeed attached to the thing..
 */
int ptrace_check_attach(pcb_t child, int kill)
{
    if (!(child->group_leader->ptrace & PT_PTRACED))
        return -ESRCH;

    if (child->group_leader->parent != current)
        return -ESRCH;

    if (!kill)
    {
        // if (child->state != TASK_STOPPED)
        // 	return -ESRCH;
        wait_task_inactive(child);
    }

    /* All systems go.. */
    return 0;
}

void force_sig_specific(int sig, pcb_t pcb)
{
    pid_t pid = get_process_pid(pcb);
    kernel_signal_kill(pid, TO_PROCESS, sig, SI_KERNEL, NULL);
}

int security_ptrace(pcb_t parent, pcb_t child)
{
    return 0;
}

int ptrace_attach(pcb_t pcb)
{
    int retval;

    // task_lock(task);
    ttosDisableTaskDispatchWithLock();

    retval = -EPERM;
    int pid = get_process_pid(pcb);

    if (pid <= 1)
        goto bad;
    if (pcb == current)
        goto bad;

    /* the same process cannot be attached many times */
    if (pcb->group_leader->ptrace & PT_PTRACED)
        goto bad;
    retval = security_ptrace(current, pcb);
    if (retval)
        goto bad;

    /* Go */
    pcb->group_leader->ptrace |= PT_PTRACED;
    // if (capable(CAP_SYS_PTRACE))
    // 	task->ptrace |= PT_PTRACE_CAP;
    // task_unlock(task);

    // write_lock_irq(&tasklist_lock);
    __ptrace_link(pcb, current);
    // write_unlock_irq(&tasklist_lock);
    ttosEnableTaskDispatchWithLock();

    force_sig_specific(SIGSTOP, pcb);
    return 0;

bad:
    // task_unlock(task);
    ttosEnableTaskDispatchWithLock();
    return retval;
}

int ptrace_detach(pcb_t child, unsigned int data)
{
    if ((unsigned long)data > _NSIG)
        return -EIO;

    /* Architecture-specific hardware disable .. */
    ptrace_disable(child);

    /* .. re-parent .. */
    // child->exit_code = data;

    // write_lock_irq(&tasklist_lock);
    // __ptrace_unlink(child);
    // /* .. and wake it up. */
    // if (child->state != TASK_ZOMBIE)
    // 	wake_up_process(child);
    // write_unlock_irq(&tasklist_lock);

    return 0;
}

static int ptrace_setoptions(pcb_t child, long data)
{
    child->ptrace &= ~PT_TRACE_MASK;

    // if (data & PTRACE_O_TRACESYSGOOD)
    // 	child->ptrace |= PT_TRACESYSGOOD;

    if (data & PTRACE_O_TRACEFORK)
    {
        child->ptrace |= PT_TRACE_FORK;
        // return -ENOSYS;
    }

    if (data & PTRACE_O_TRACEVFORK)
    {
        child->ptrace |= PT_TRACE_VFORK;
        // return -ENOSYS;
    }

    if (data & PTRACE_O_TRACECLONE)
        child->ptrace |= PT_TRACE_CLONE;

    if (data & PTRACE_O_TRACEEXEC)
        child->ptrace |= PT_TRACE_EXEC;

    if (data & PTRACE_O_TRACEVFORKDONE)
        child->ptrace |= PT_TRACE_VFORK_DONE;

    if (data & PTRACE_O_TRACEEXIT)
        child->ptrace |= PT_TRACE_EXIT;

    return (data & ~PTRACE_O_MASK) ? -EINVAL : 0;
}

static int ptrace_getsiginfo(pcb_t child, siginfo_t __user *data)
{
    return copy_to_user(data, &child->ptrace_siginfo, sizeof(ksiginfo_t));
}

int ptrace_request(pcb_t child, long request, long addr, long data)
{
    int ret = -EIO;

    switch (request)
    {
#ifdef PTRACE_OLDSETOPTIONS
    case PTRACE_OLDSETOPTIONS:
#endif
    case PTRACE_SETOPTIONS:
        ret = ptrace_setoptions(child, data);
        break;
    case PTRACE_GETEVENTMSG:
        ret = put_user(&child->group_leader->ptrace_message, (unsigned long __user *)data);
        break;
    case PTRACE_GETSIGINFO:
        ret = ptrace_getsiginfo(child, (siginfo_t __user *)data);
        break;
    case PTRACE_SETSIGINFO:
        // ret = ptrace_setsiginfo(child, (siginfo_t __user *) data);
        KLOG_E("request id:0x%lx is not implement", request);
        break;
    default:
        KLOG_E("request id:0x%lx is not implement", request);
        break;
    }

    return ret;
}

static int wake_up_process(pcb_t pcb)
{
    TTOS_SignalResumeTask(pcb->taskControlId);
    return 0;
}

/*
 * this routine will put a word on the processes privileged stack.
 * the offset is how far from the base addr as stored in the THREAD.
 * this routine assumes that all the privileged stacks are in our
 * data space.
 */
static int put_user_reg(pcb_t pcb, int offset, long data)
{
    struct user *regs = get_user_regs(pcb);
    int ret = -EINVAL;

    ((long *)regs)[offset] = data;

    if (valid_user_regs(regs))
    {
        ((long *)regs)[offset] = data;
        ret = 0;
        set_user_regs(pcb, regs);
    }

    if (regs)
    {
        free(regs);
        regs = NULL;
    }

    return ret;
}

/*
 * Read the word at offset "off" into the "struct user".  We
 * actually access the pt_regs stored on the kernel stack.
 */
static int ptrace_read_user(pcb_t pcb, unsigned long off, unsigned long *ret)
{
    unsigned long tmp;
    if (off & 3 || off > sizeof(struct user))
    {
        return -EIO;
    }

    tmp = 0;
    if (off < sizeof(struct user))
    {
        tmp = get_user_reg(pcb, off / sizeof(long));
    }

    put_user(&tmp, ret);
    return 0;
}

/*
 * Write the word at offset "off" into "struct user".  We
 * actually access the pt_regs stored on the kernel stack.
 */
static int ptrace_write_user(pcb_t pcb, unsigned long off, unsigned long val)
{
    if (off & 3 || off > sizeof(struct user))
    {
        return -EIO;
    }

    if (off >= sizeof(struct user))
    {
        return 0;
    }

    return put_user_reg(pcb, off / sizeof(long), val);
}

static int do_ptrace(int request, pcb_t child, long addr, long data)
{
    unsigned long tmp;
    int ret = 0;
    switch (request)
    {
    /*
     * read word at location "addr" in the child process.
     */
    case PTRACE_PEEKTEXT:
    case PTRACE_PEEKDATA:
        ret = access_process_vm(child, addr, &tmp, sizeof(unsigned long), 0);
        if (ret == sizeof(unsigned long))
            ret = put_user(&tmp, (unsigned long *)data);
        else
            ret = -EIO;
        break;

    case PTRACE_PEEKUSER:
        ret = ptrace_read_user(child, addr, (unsigned long *)data);
        if (ret != 0)
        {
            tmp = ret;
        }
        break;

    /*
     * write the word at location addr.
     */
    case PTRACE_POKETEXT:
    case PTRACE_POKEDATA:
        ret = access_process_vm(child, addr, &data, sizeof(unsigned long), 1);
        if (ret == sizeof(unsigned long))
            ret = 0;
        else
            ret = -EIO;
        break;

    case PTRACE_POKEUSER:
        ret = ptrace_write_user(child, addr, data);
        break;

    /*
     * continue/restart and stop at next (return from) syscall
     */
    case PTRACE_SYSCALL:
    case PTRACE_CONT:
        ret = -EIO;
        if ((unsigned long)data > _NSIG)
            break;
        if (request == PTRACE_SYSCALL)
            set_tsk_thread_flag(child, TIF_SYSCALL_TRACE);
        else
            clear_tsk_thread_flag(child, TIF_SYSCALL_TRACE);
        // child->exit_code = data;
        /* make sure single-step breakpoint is gone. */
        child->ptrace &= ~PT_SINGLESTEP;
        ptrace_cancel_bpt(child);
        wake_up_process(child);
        ret = 0;
        break;

    /*
     * make the child exit.  Best I can do is send it a sigkill.
     * perhaps it should be put in the status that it wants to
     * exit.
     */
    case PTRACE_KILL:
        /* make sure single-step breakpoint is gone. */
        child->ptrace &= ~PT_SINGLESTEP;
        ptrace_cancel_bpt(child);
        pid_t pid = get_process_pid(child);
        kernel_signal_kill(pid, TO_PROCESS, SIGKILL, SI_USER, NULL);
        // if (child->state != TASK_ZOMBIE) {
        // 	child->exit_code = SIGKILL;
        // 	wake_up_process(child);
        // }
        ret = 0;
        break;

    /*
     * execute single instruction.
     */
    case PTRACE_SINGLESTEP:
        ret = -EIO;
        if ((unsigned long)data > _NSIG)
            break;
        child->ptrace |= PT_SINGLESTEP;
        clear_tsk_thread_flag(child, TIF_SYSCALL_TRACE);
        // child->exit_code = data;
        ptrace_set_bpt(child);

        /* give it a chance to run. */
        wake_up_process(child);
        ret = 0;
        break;

    case PTRACE_DETACH:
        // ret = ptrace_detach(child, data);
        KLOG_E("request id:0x%x is not implement", request);
        break;
    case PTRACE_GETREGSET:
        /* 硬件观察点寄存器 */
        if (NT_ARM_HW_WATCH == addr)
        {
#ifdef __aarch64__
            ret = ptrace_hw_watch_get(child, (struct iovec __user *)data);
#else
            ret = -ENOSYS;
#endif
        }
        else
            /* 硬件断点寄存器 */
            if (NT_ARM_HW_BREAK == addr)
            {
#ifdef __aarch64__
                ret = ptrace_hw_debug_get(child, (struct iovec __user *)data);
#else
                ret = -ENOSYS;
#endif
            }
            else
            {
                ret = ptrace_getregset(child, (void *)data, addr);
            }
        break;
    case PTRACE_SETREGSET:
        /* 硬件观察点寄存器 */
        if (NT_ARM_HW_WATCH == addr)
        {
#ifdef __aarch64__
            ret = ptrace_hw_watch_set(child, (struct iovec __user *)data);
#else
            ret = -ENOSYS;
#endif
        }
        else
            /* 硬件断点寄存器 */
            if (NT_ARM_HW_BREAK == addr)
            {
#ifdef __aarch64__
                ret = ptrace_hw_debug_set(child, (struct iovec __user *)data);
#else
                ret = -ENOSYS;
#endif
            }
            else
            {
                ret = ptrace_setregset(child, (void *)data, addr);
            }
        break;
    case PTRACE_GETREGS:
        ret = ptrace_getregset(child, (void *)data, 0);
        break;

    case PTRACE_SETREGS:
        ret = ptrace_setregset(child, (void *)data, 0);
        break;

    case PTRACE_GETFPREGS:
        // ret = ptrace_getfpregs(child, (void *)data);
        KLOG_E("request id:0x%x is not implement", request);
        break;

    case PTRACE_SETFPREGS:
        // ret = ptrace_setfpregs(child, (void *)data);
        KLOG_E("request id:0x%x is not implement", request);
        break;
    /* 硬件断点寄存器 */
    case PTRACE_GETHBPREGS:
        KLOG_E("GETHBPREGS request id:0x%x is not implement", request);
        ret = -ENOSYS;
        break;
    /* 硬件断点寄存器 */
    case PTRACE_SETHBPREGS:
        KLOG_E("SETHBPREGS request id:0x%x is not implement", request);
        ret = -ENOSYS;
        break;
    default:
        ret = ptrace_request(child, request, addr, data);
        break;
    }

    return ret;
}

int sys_ptrace(long request, long pid, long addr, long data)
{
    int ret = 0;

    pcb_t child;
    TASK_ID task;

    ttosDisableTaskDispatchWithLock();
    ret = -EPERM;

    /* 被调试进程 */
    if (request == PTRACE_TRACEME)
    {
        /* are we already being traced? */
        if (current->group_leader->ptrace & PT_PTRACED)
        {
            goto out;
        }

        ret = security_ptrace(current->group_leader->parent, current);
        if (ret)
        {
            goto out;
        }
        /* set the ptrace bit in the process flags. */
        current->group_leader->ptrace |= PT_PTRACED;
        ret = 0;
        goto out;
    }

    ret = -ESRCH;
    if (pid == 0)
    {
        task = current->first_child->taskControlId; // ttosGetRunningTask();
    }
    else
    {
        task = task_get_by_tid(pid);
    }

    if (!task)
    {
        printk("%s %d %d task_get_by_tid failed\n", __func__, __LINE__, (pid_t)pid);
        goto out;
    }

    child = (pcb_t)task->ppcb;
    if (!child)
    {
        printk("%s %d task_get_by_tid failed\n", __func__, __LINE__);
        goto out;
    }

    ret = -EPERM;

    /* 跳过init进程 */
    if (pid == 1)
    {
        goto out;
    }

    if (request == PTRACE_ATTACH)
    {
        ret = ptrace_attach(child);
        goto out;
    }

    ret = ptrace_check_attach(child, request == PTRACE_KILL);
    if (ret == 0)
    {
        ret = do_ptrace(request, child, addr, data);
    }

out:

    ttosEnableTaskDispatchWithLock();

    return ret;
}

/* set thread flags in other task's structures
 * - see asm/thread_info.h for TIF_xxxx flags available
 */
void set_tsk_thread_flag(pcb_t pcb, int flag) {}

void clear_tsk_thread_flag(pcb_t pcb, int flag) {}