/**
 * @file    kernel/process/process_group.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * 0c106b3f 2024-07-24 修改规范INIT API
 * 791ac4ef 2024-07-12 修复线程bug
 * a91d82ae 2024-07-09 在删除互斥锁前释放持有的互斥锁
 * 2572306a 2024-07-09 修复可能造成释放内存后还在使用的bug
 * ac006b61 2024-07-02 移除一级ttos目录
 * 5da3da96 2024-06-26 修改进程业务逻辑 只有leader 才有父子进程树
 * 2d8e98f1 2024-06-25 调整打印级别
 * 03f6ba78 2024-06-12 修复线程组任务退出机制，由强制删除改为发送信号
 * 4dc228c0 2024-06-07 处理进程退出
 * b9d7bcd8 2024-06-06 修改调试打印等级
 * ef4a0f3b 2024-06-05 更新日志打印
 * 023249b2 2024-06-04 修复进程组删除时未移除链表节点问题
 * 3ad5e448 2024-06-04 临时提交
 * 4bb423b7 2024-05-30 1.添加进程组 2.支持信号发送到进程组
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include <assert.h>
#include <atomic.h>
#include <errno.h>
#include <list.h>
#include <tglock.h>
#include <ttosBase.h>
#include <ttosProcess.h>
#include <ttos_init.h>

#undef KLOG_TAG
#define KLOG_TAG "pgroup"
#include <klog.h>

static struct list_head process_group_list;
static ttos_spinlock_t pgroup_list_lock;

int pgrp_init(void)
{
    INIT_LIST_HEAD(&process_group_list);
    spin_lock_init(&pgroup_list_lock);

    return 0;
}

static void pgrp_inc_ref(pgroup_t pgrp)
{
    atomic_add(&(pgrp->ref), 1);
}

static void pgrp_dec_ref(pgroup_t pgrp)
{
    atomic_add(&pgrp->ref, -1);
}

static int pgrp_dec_get(pgroup_t pgrp)
{
    return pgrp->ref.counter;
}

pgroup_t pgrp_find_and_inc_ref(pid_t pgid)
{
    pgroup_t group;

    group = process_pgrp_find(pgid);
    if (group)
    {
        atomic_add(&(group->ref), 1);
    }

    return group;
}

pgroup_t process_pgrp_find(pid_t pgid)
{
    pcb_t pcb;
    long flag = 0;
    pgroup_t group = NULL;

    /* parameter check */
    if (pgid < 0)
    {
        return NULL;
    }

    if (pgid == 0)
    {
        pcb = ttosProcessSelf();
        if (pcb)
        {
            pgid = get_process_pid(ttosProcessSelf());
        }
        else
        {
            return NULL;
        }
    }

    /* enter critical */
    spin_lock_irqsave(&pgroup_list_lock, flag);

    /* try to find process group */
    list_for_each_entry(group, &process_group_list, global_pgroup_node)
    {
        if (group->pgid == pgid)
        {
            spin_unlock_irqrestore(&pgroup_list_lock, flag);

            return group;
        }
    }

    spin_unlock_irqrestore(&pgroup_list_lock, flag);

    return NULL;
}

pgroup_t pgrp_create(pcb_t leader)
{
    pgroup_t group = NULL;

    if (leader == NULL)
    {
        return NULL;
    }

    group = calloc(1, sizeof(struct pgroup));
    if (group != NULL)
    {
        /* 进程组加入全局进程组链表 */
        list_add(&(group->global_pgroup_node), &process_group_list);

        INIT_LIST_HEAD(&(group->process));

        TTOS_CreateMutex(1, 0, &(group->lock));

        group->leader = leader;
        group->sid = 0;
        group->is_orphaned = 0;
        group->pgid = get_process_pid(leader);
    }

    KLOG_D("process:%p create group:%p pgid:%d", leader, group, group->pgid);

    return group;
}

int pgrp_delete(pgroup_t group)
{
    long flag;

    if (!group)
    {
        return -1;
    }

    KLOG_D("group:%p pgid:%d is delete", group, group->pgid);

    assert(pgrp_dec_get(group) == 0);

    /* enter critical */
    spin_lock_irqsave(&pgroup_list_lock, flag);
    list_del(&group->global_pgroup_node);
    spin_unlock_irqrestore(&pgroup_list_lock, flag);

    /* 是当前任务拥有互斥锁 */
    if (current_task() == group->lock->semHolder)
    {
        /* 释放互斥锁 */
        TTOS_ReleaseMutex(group->lock);
    }

    TTOS_DeleteMutex(group->lock);

    group->pgid = 0;
    free(group);

    return 0;
}

int pgrp_insert(pgroup_t group, pcb_t process)
{
    long flag = 0;

    /* parameter check */
    if (group == NULL || process == NULL)
    {
        return -EINVAL;
    }

    TTOS_ObtainMutex(group->lock, TTOS_WAIT_FOREVER);
    pcb_lock(process, &flag);

    process->pgid = group->pgid;
    process->pgrp = group;
    process->sid = group->sid;
    list_add_after(&(process->pgrp_node), &(group->process));

    pgrp_inc_ref(group);

    pcb_unlock(process, &flag);
    TTOS_ReleaseMutex(group->lock);

    KLOG_D("process:%p insert group:%p", process, group);

    return 0;
}

bool pgrp_remove(pgroup_t group, pcb_t process)
{
    long flag = 0;
    bool is_empty = false;

    /* parameter check */
    if (group == NULL || process == NULL)
    {
        return -EINVAL;
    }

    TTOS_ObtainMutex(group->lock, TTOS_WAIT_FOREVER);
    pcb_lock(process, &flag);

    list_del(&(process->pgrp_node));
    /* clear children sid and pgid */
    process->pgrp = NULL;
    process->pgid = 0;
    process->sid = 0;

    pgrp_dec_ref(group);

    pcb_unlock(process, &flag);

    is_empty = list_empty(&(group->process));

    TTOS_ReleaseMutex(group->lock);

    KLOG_D("process:%p remove from group:%p", process, group);

    if (is_empty)
    {
        pgrp_delete(group);
        return true;
    }

    return false;
}

pid_t pgid_get_bypgrp(pgroup_t group)
{
    return group ? group->pgid : 0;
}

int pgrp_move(pgroup_t group, pcb_t process)
{
    long flag = 0;
    int retry = 1;
    bool is_delete = false;
    pgroup_t old_group;

    /* parameter check */
    if (group == NULL || process == NULL)
    {
        return -EINVAL;
    }

    if (pgid_get_bypgrp(group) == process_pgid_get_byprocess(process))
    {
        return 0;
    }

    TTOS_ObtainMutex(group->lock, TTOS_WAIT_FOREVER);

    while (retry)
    {
        retry = 0;
        old_group = process_pgrp_find(process_pgid_get_byprocess(process));

        TTOS_ObtainMutex(old_group->lock, TTOS_WAIT_FOREVER);
        pcb_lock(process, &flag);

        if (process->pgrp == old_group)
        {
            /* 通过返回值判断是否已经将old_group释放，如果已经释放，则后续无需再释放互斥锁 */
            is_delete = pgrp_remove(old_group, process);

            pgrp_insert(group, process);
        }
        else
        {
            retry = 1;
        }

        if (!is_delete)
        {
            TTOS_ReleaseMutex(old_group->lock);
        }

        is_delete = false;

        pcb_unlock(process, &flag);
    }

    TTOS_ReleaseMutex(group->lock);

    return 0;
}

void foreach_pgrp_process(foreach_pgrp_func func, pgroup_t group, void *param)
{
    pcb_t pcb;

    TTOS_ObtainMutex(group->lock, TTOS_WAIT_FOREVER);
    list_for_each_entry(pcb, &(group->process), pgrp_node)
    {
        func(pcb, param);
    }
    TTOS_ReleaseMutex(group->lock);
}

int pgrp_update_children_info(pgroup_t group, pid_t sid, pid_t pgid)
{
    long flag = 0;
    struct list_head *node = NULL;
    pcb_t process = NULL;

    if (group == NULL)
    {
        return -EINVAL;
    }

    TTOS_ObtainMutex(group->lock, TTOS_WAIT_FOREVER);

    /* try to find process group */
    list_for_each(node, &(group->process))
    {
        process = (pcb_t)list_entry(node, struct T_TTOS_ProcessControlBlock, pgrp_node);
        pcb_lock(process, &flag);
        if (sid != -1)
        {
            process->sid = sid;
        }
        if (pgid != -1)
        {
            process->pgid = pgid;
            process->pgrp = group;
        }
        pcb_unlock(process, &flag);
    }

    TTOS_ReleaseMutex(group->lock);
    return 0;
}

/**
 * setpgid() sets the PGID of the process specified by pid to pgid.
 *  If pid is zero, then the process ID of the calling process is used.
 *  If pgid is zero, then the PGID of the process specified by pid is made the
 * same as its process ID. If setpgid() is used to move a process from one
 * process group to another (as is done by some shells when creating pipelines),
 * both process groups must be part of the same session (see setsid(2) and
 * credentials(7)). In this case, the pgid specifies an existing process group
 * to be joined and the session ID of that group must match the session ID of
 * the joining process.
 */
int setpgid(pid_t pid, pid_t pgid)
{
    long flag = 0;
    pcb_t process, self_process;
    pgroup_t group = NULL;
    int err = 0;

    self_process = ttosProcessSelf();

    if (pid == 0)
    {
        pid = get_process_pid(self_process);
        process = self_process;
    }
    else
    {
        process = pcb_get_by_pid(pid);

        if (process == NULL)
        {
            return -ESRCH;
        }
    }

    if (pgid == 0)
    {
        pgid = pid;
    }
    if (pgid < 0)
    {
        return -EINVAL;
    }

    pcb_lock(process, &flag);

    if (process->group_leader->parent == self_process)
    {
        if (process->flags & FORKNOEXEC)
        {
            err = -EACCES;
            pcb_unlock(process, &flag);
            goto exit;
        }
    }
    else
    {
        /**
         * pid is not the calling process and not a child of the calling
         * process.
         */
        if (process != self_process)
        {
            err = -ESRCH;
            pcb_unlock(process, &flag);
            goto exit;
        }
    }

    pcb_unlock(process, &flag);

    if (pgid != pid)
    {
        group = process_pgrp_find(pgid);
        if (group == NULL)
        {
            group = pgrp_create(process);
            pgrp_move(group, process);
        }
        else
        {
            pgrp_move(group, process);
        }
    }
    else
    {
        group = process_pgrp_find(pgid);
        if (group == NULL)
        {
            group = pgrp_create(process);
            pgrp_move(group, process);
        }
        else
        {
            if (process->pgid != group->pgid)
            {
                pgrp_move(group, process);
            }
            err = 0;
        }
    }

exit:
    return err;
}

void foreach_task_group(pcb_t pcb, void (*func)(pcb_t, void *), void *param)
{
    long flags;
    pcb_t thread;
    pcb_t next;

    tg_lock(pcb->group_leader);
    list_for_each_entry_safe(thread, next, &pcb->group_leader->thread_group, sibling_node)
    {
        func(thread, param);
    }
    tg_unlock(pcb->group_leader);
}

static void exit_task(pcb_t pcb, void *param)
{
    /* 忽略已经或即将退出的进程 */
    if (pcb->exit_state)
    {
        return;
    }

    if (pcb == pcb->group_leader)
    {
        return;
    }
    if (pcb->taskControlId)
    {
        /* 这里开锁 避免死锁 */
        // tg_unlock(pcb->group_leader);
        // kernel_signal_kill(pcb->taskControlId->tid, TO_THREAD, SIGKILL, SI_KERNEL, NULL);
        // tg_lock(pcb->group_leader);

        kernel_signal_kill_with_worker(pcb->taskControlId->tid, TO_THREAD, SIGKILL, SI_KERNEL,
                                       NULL);
    }
}

long process_exit_group(int exit_code, bool is_normal_exit)
{
    pcb_t self = ttosProcessSelf();
    assert(self != NULL);

    pcb_t leader = self->group_leader;
    assert(leader != NULL);

    if (self != leader)
    {
        leader->group_exit_status.group_request_terminate = true;
        leader->group_exit_status.exit_code = exit_code;
        leader->group_exit_status.is_normal_exit = is_normal_exit;

        assert(leader->taskControlId != NULL);

        kernel_signal_kill(leader->taskControlId->tid, TO_THREAD, SIGKILL, SI_KERNEL, NULL);
        if (is_normal_exit)
        {
            self->exit_code = exit_code;
            process_exit(self);
        }
        else
        {
            self->exit_signal = exit_code;
            process_exit(self);
        }

        return 0;
    }

    if (leader->group_exit_status.is_terminated)
    {
        return 0;
    }

    leader->group_exit_status.exit_code = exit_code;
    leader->group_exit_status.is_terminated = true;
    leader->group_exit_status.is_normal_exit = is_normal_exit;

    foreach_task_group(self, exit_task, NULL);

    // todo wait all thread exit
    while (1)
    {
        int thread_cnt;
        thread_cnt = list_count(&leader->thread_group);
        if (1 == thread_cnt)
        {
            break;
        }
        else
        {
            TTOS_SleepTask(2);
        }
    }

    if (is_normal_exit)
    {
        self->exit_code = exit_code;
        process_exit(self);
    }
    else
    {
        self->exit_signal = exit_code;
        process_exit(self);
    }
    return 0;
}

/**
 * getpgid() returns the PGID of the process specified by pid.
 *  If pid is zero, the process ID of the calling process is used. (Retrieving
 * the PGID of a process other than the caller is rarely necessary, and the
 * POSIX.1 getpgrp() is preferred for that task.)
 */
int sys_getpgid(pid_t pid)
{
    pcb_t process;

    process = pcb_get_by_pid(pid);

    if (process == NULL)
    {
        return -ESRCH;
    }

    return process_pgid_get_byprocess(process);
}

INIT_EXPORT_SYS(pgrp_init, "pgrp_init");
