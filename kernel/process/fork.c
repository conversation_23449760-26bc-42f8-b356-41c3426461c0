/**
 * @file    kernel/process/fork.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * 0bf375fe 2024-07-19 修复time部分行为逻辑
 * 914d9323 2024-07-11 添加周期任务支持
 * 420a17f6 2024-07-03 修改set_child_tid到父进程
 * 3511844a 2024-07-02 移除include路径中的trace
 * ac006b61 2024-07-02 移除一级ttos目录
 * 92e22a0f 2024-07-01 添加内核tid 以解决tid反查task的问题
 * a8d0ee58 2024-07-01 修改内核进程栈大小
 * c28d4fb2 2024-06-28 子线程共用mmap_root
 * c072c961 2024-06-27 修复 clone 传入sp 未处理
 * 5da3da96 2024-06-26 修改进程业务逻辑 只有leader 才有父子进程树
 * 2d8e98f1 2024-06-25 调整打印级别
 * 5b07e397 2024-06-22 添加cmdline
 * df703b2a 2024-06-19 1. trace event模板优化 2.
 * 新增跟踪mutex、fork、中断的tracepoint 3. tracing_log.h不再依赖ttos.h头文件
 * f5a21f58 2024-06-07 修复创建线程异常
 * d6e8be2d 2024-06-06 修复拼写错误
 * 1f357380 2024-06-06 signal bug fix
 * ef4a0f3b 2024-06-05 更新日志打印
 * 3ad5e448 2024-06-04 临时提交
 * 4bb423b7 2024-05-30 1.添加进程组 2.支持信号发送到进程组
 * ae75b13f 2024-05-24 代码整理
 * 3823374e 2024-05-23 添加signal功能模块
 * 6cf7e612 2024-05-16 同一 clone接口 格式化代码
 * 8b222f6e 2024-05-16 添加 do_fork动作 标准话fork vfork行为
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * d33c0b03
 * 2024-05-07 1.修复fork出的子进程为关调度状态的问题 2.修复释放信号量接口中，信号量的最大值比较有误的问题
 * 3.修复pipe互斥锁初值设置有误的问题 1bf3395b 2024-05-06
 * 解决内核起用户进程适配新k库 efb16eaa 2024-04-28 解决内核栈释放的问题 ff1adfc2
 * 2024-04-28 解决由于栈过小导致溢出的问题 2815c377 2024-04-28 增加
 * waitpid的支持 fe0becbf 2024-04-28 增加fork系统调用
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include <assert.h>
#include <context.h>
#include <errno.h>
#include <inttypes.h>
#include <period_sched_group.h>
#include <ptrace/ptrace.h>
#include <stdio.h>
#include <sys/ptrace.h>
#include <sys/wait.h>
#include <tglock.h>
#include <time/ktime.h>
#include <trace/tracing.h>
#include <ttosBase.h>
#include <ttosProcess.h>
#include <uaccess.h>

#define KLOG_TAG "FORK"
#include <klog.h>

extern void kernel_set_tls(uintptr_t tls);
uintptr_t kernel_get_tls(void);
void restore_context(void *context);
void set_context_type(struct arch_context *context, int type);

static void clone_task_entry(void *param)
{
    pcb_t pcb = ttosProcessSelf();
    assert(pcb != NULL);
    kernel_set_tls(pcb->tls);
    if (pcb->group_leader == pcb)
    {
        process_tid_set_to_pid(pcb);
    }

    if (pcb->ptrace & PT_PTRACED)
    {
        ptrace_notify(SIGSTOP);
    }

    arch_context_set_return(&pcb->exception_context, 0);
    mm_switch_space_to(get_process_mm(pcb));
    arch_switch_context_set_stack(&pcb->taskControlId->switchContext,
                                  (long)pcb->taskControlId->kernelStackTop);

    set_context_type(&pcb->exception_context, IRQ_CONTEXT);
    restore_context(&pcb->exception_context);
}

static bool check_clone_flags(unsigned long clone_flags)
{
    if (clone_flags & CLONE_THREAD)
    {
        if (!(clone_flags & CLONE_SIGHAND))
        {
            return false;
        }
    }
    if (clone_flags & CLONE_SIGHAND)
    {
        if (!(clone_flags & CLONE_VM))
        {
            return false;
        }
    }
    return true;
}

/* 表示当前进程vfork完成 */
static void complete_vfork_done(pcb_t pcb)
{
    T_TTOS_CompletionControl *vfork;
    long flags;

    pcb_lock(pcb, &flags);
    vfork = pcb->vfork_done;
    if (likely(vfork))
    {
        pcb->vfork_done = NULL;
        TTOS_ReleaseCompletion(vfork);
    }
    pcb_unlock(pcb, &flags);
}

void vfork_exec_wake(pcb_t pcb)
{
    complete_vfork_done(pcb);
}

void vfork_exit_wake(pcb_t pcb)
{
    complete_vfork_done(pcb);
}

pid_t do_fork(unsigned long clone_flags, unsigned long newsp, int __user *set_child_tid,
              int __user *clear_child_tid, unsigned long tls, struct period_param *param)
{
    T_TTOS_ConfigTask taskParam;
    TASK_ID child_task, parent_task;
    T_TTOS_ReturnCode ret;
    pid_t child_pid;
    pgroup_t group;
    T_TTOS_CompletionControl vfork;
    long flags;
    bool is_period_task = false;
    pcb_t caller;
    bool is_clone = true;

    if (!check_clone_flags(clone_flags))
    {
        return -1;
    }

    caller = ttosProcessSelf();

    if (caller == NULL)
    {
        return -EPERM;
    }

    pcb_t parent = caller->group_leader;
    assert(parent != NULL);
    parent_task = caller->taskControlId;
    assert(parent_task != NULL);

    /* 创建一个新的进程控制块 */
    pcb_t child = memalign(8, sizeof(struct T_TTOS_ProcessControlBlock));

    if (child == NULL)
    {
        KLOG_E("%s malloc child pcb failed, size:0x%" PRIxPTR, __func__,
               sizeof(struct T_TTOS_ProcessControlBlock));
        return -ENOMEM;
    }

    /* 拷贝进程控制块 */
    memcpy(child, caller, sizeof(*child));
#ifdef __aarch64__
    memset(&child->debugbp, 0, sizeof(child->debugbp));
    memset(&child->debugwr, 0, sizeof(child->debugwr));
#endif
    /* 初始化tglock */
    tg_lock_create(child);

    /* 重新初始化进程对象链 */
    INIT_LIST_HEAD(&child->obj_list);

    child->vfork_done = NULL;
    INIT_LIST_HEAD(&child->posix_timers);

    /* 创建一个新的进程, 属性从原来的线程复制 */
    memset(&taskParam, 0, sizeof(taskParam));
    strncpy((char *)taskParam.cfgTaskName, (const char *)parent_task->objCore.objName,
            sizeof(taskParam.cfgTaskName));
    taskParam.tickSliceSize = parent_task->tickSliceSize;
    taskParam.taskType = parent_task->taskType;
    taskParam.taskPriority = parent_task->taskPriority;
    taskParam.arg = NULL;
    taskParam.preempted = (parent_task->preemptedConfig == 0) ? true : false;
    taskParam.autoStarted = FALSE;
    taskParam.taskType = TTOS_SCHED_NONPERIOD;

    if (param)
    {
        if (param->durationTime > param->periodTime)
        {
            free(child);
            return -EINVAL;
        }

        taskParam.periodTime = param->periodTime;
        taskParam.durationTime = param->durationTime;
        taskParam.delayTime = param->delayTime;
        taskParam.taskType = TTOS_SCHED_PERIOD;
        is_period_task = true;

        /* 强制延迟启动，因为若不延迟启动，则可能在一个tick中间开始执行，任务还未执行1tick时间，tick中断就产生，导致超时*/
        if (param->autoStarted && !taskParam.delayTime)
        {
            taskParam.delayTime = 1;
        }
    }

    /* 建立新的内核栈 */
    taskParam.stackSize = parent_task->kernelStackTop - parent_task->stackBottom;
    taskParam.stackSize = taskParam.stackSize < CONFIG_KERNEL_PROCESS_STACKSIZE
                              ? CONFIG_KERNEL_PROCESS_STACKSIZE
                              : taskParam.stackSize;
    taskParam.taskStack = memalign(DEFAULT_TASK_STACK_ALIGN_SIZE, taskParam.stackSize);
    if (taskParam.taskStack == NULL)
    {
        KLOG_E("%s malloc stack failed, stack size:0x%x", __func__, taskParam.stackSize);
        return -ENOMEM;
    }
    taskParam.isFreeStack = TRUE;
    taskParam.entry = (T_TTOS_TaskRoutine)clone_task_entry;
    strcpy((char *)taskParam.objVersion, TTOS_OBJ_CONFIG_CURRENT_VERSION);

    /* 创建新任务 */
    ret = TTOS_CreateTask(&taskParam, &child_task);
    if (ret != TTOS_OK)
    {
        KLOG_E("create task error %d", ret);
        return -ENOMEM;
    }

    child_task->ppcb = child;
    child->taskControlId = child_task;
    child->tls = kernel_get_tls();

    /* 设置新的sp */
    if (newsp != 0)
    {
        child->userStack = (void *)newsp;
        arch_context_set_stack(&child->exception_context, (long)child->userStack);

        struct mm_region *region = mm_va2region(get_process_mm(child), newsp);
        if (region == NULL)
        {
            return -EINVAL;
        }
        region->flags |= MAP_IS_STACK;
    }
    else
    {
        is_clone = false;
    }

    if (clone_flags & CLONE_THREAD)
    {
        /* 同一个线程组 不创建pid */
        if (pid_obj_ref(parent, child))
        {
            return -ENOMEM;
        }
        /* futex 原则上也是没有复制的 */
        process_ref_futex_root(parent, child);
        process_wait_info_ref(parent, child);
        child->cmdline = process_obj_ref(child, parent->cmdline);
        child->parent = NULL;
        child->sibling = NULL;
        child->first_child = NULL;
        KLOG_D("pcb:%p create thread pcb:%p", parent, child);

        arch_context_thread_init(&child->exception_context);

        // task_create_procfs_dir(get_process_pid(child), child->taskControlId->tid);
    }
    else
    {
        /* 创建新的pid */
        child->pid = pid_obj_alloc(child);
        if (child->pid == NULL)
        {
            KLOG_E("%s %d:alloc pid failed", __func__, __LINE__);
            return -ENOMEM;
        }
        /* 创建新的futex */
        if (process_create_futex_root(child))
        {
            return -ENOMEM;
        }

        child->cmdline =
            process_obj_create(child, strdup((char *)parent->cmdline->really->obj), free);

        if (child->cmdline == NULL)
        {
            return -ENOMEM;
        }

        process_wait_info_create(child);
        if (child->wait_info == NULL)
        {
            KLOG_E("%s %d:alloc wait_info failed", __func__, __LINE__);
            return -ENOMEM;
        }

        KLOG_D("pcb:%p create pcb:%p", parent, child);
        /* 只有完整进程才存在父子关系 */
        child->parent = parent;
        child->sibling = parent->first_child;
        parent->first_child = child;
        child->first_child = NULL;

        /* 避免子进程被trace 后面会再做处理 */
        child->ptrace = 0;

        // task_create_procfs_dir(get_process_pid(child), get_process_pid(child));
    }

    child->flags |= FORKNOEXEC;

    /* 复制文件列表 */
    if (clone_flags & CLONE_FILES)
    {
        process_filelist_ref(parent, child);
    }
    else
    {
        process_filelist_copy(parent, child);
    }

    if (clone_flags & CLONE_VM)
    {
        /* 共享mmu表 */
        process_mm_ref(parent, child);
    }
    else
    {
        /* 复制整个mmu表 */
        process_mm_copy(parent, child);
        /* 设置新的 ASID */
        get_process_mm(child)->asid = get_process_pid(child);
    }

    /* execve 了才换 不需要考虑是否是同一进程组 */
    child->envp = process_obj_ref(child, parent->envp);

    if (clone_flags & CLONE_PARENT_SETTID)
    {
        child->set_child_tid = set_child_tid;
    }
    else
    {
        child->set_child_tid = NULL;
    }

    if (clone_flags & CLONE_SETTLS)
    {
        child->tls = tls;
    }

    if (clone_flags & CLONE_CHILD_CLEARTID)
    {
        child->clear_child_tid = clear_child_tid; /* 子进程退出时，清除子进程的线程 ID */
    }

    /* 初始化线程组链表 */
    INIT_LIST_HEAD(&(child->thread_group));

    if (clone_flags & CLONE_THREAD)
    {
        child->tgid = parent->tgid;
        child->group_leader = parent;
        tg_lock(parent);
        list_add_after(&child->sibling_node, &parent->thread_group);
        tg_unlock(parent);
    }
    else
    {
        /* 查找父进程所在的进程组 */
        group = process_pgrp_find(process_pgid_get_byprocess(parent));
        if (group)
        {
            /* 子进程加入进程组 */
            pgrp_insert(group, child);
        }
        else
        {
            KLOG_E("fail at %s:%d", __FILE__, __LINE__);
        }

        /* 主线程为线程组leader */
        child->group_leader = child;

        /* leader的tgid和其pid相同 */
        child->tgid = get_process_pid(child);

        tg_lock(child);
        list_add(&child->sibling_node, &child->thread_group);
        tg_unlock(child);
    }

    fork_signal(clone_flags, child);

    /* 继承父进程的控制终端 */
    get_process_signal(child)->ctty = get_process_signal(parent)->ctty;

    /* 创建pcb自旋锁 */
    INIT_SPIN_LOCK(&child->lock);

    if (is_period_task)
    {
        char group_name[128] = {0};
        sprintf(group_name, "group_period:%u", param->periodTime);
        int sched_group_id = period_sched_group_create(param->periodTime, group_name);
        period_sched_group_add(child, sched_group_id);
    }

    child_pid = get_process_pid(child);

    TRACING_OBJ_INIT(process, child);

    if (child->set_child_tid != NULL)
    {
        copy_to_user(child->set_child_tid, &child->taskControlId->tid, sizeof(int));
    }

    /* 非周期任务 || 自启动的周期任务 */
    if (!is_period_task || (is_period_task && param->autoStarted))
    {
        TTOS_ActiveTask(child_task);
    }
    else
    {
        /* 非自启动的周期任务 */
        wait_actived_task_add(child->taskControlId->tid);
    }

    if (parent->ptrace & PT_PTRACED)
    {
        if (is_clone)
        {
            if (parent->ptrace & PT_TRACE_CLONE)
            {
                parent->ptrace_message = child->taskControlId->tid;
                ptrace_notify((PTRACE_EVENT_CLONE << 8) | SIGTRAP);
            }
        }
        else if (clone_flags & CLONE_VFORK)
        {
            if (parent->ptrace & PT_TRACE_VFORK)
            {
                // 设置ptrace
                if (child == child->group_leader)
                {
                    __ptrace_link(child, parent->parent);
                }
                parent->ptrace_message = child_pid;
                ptrace_notify((PTRACE_EVENT_VFORK << 8) | SIGTRAP);
            }
        }
        else
        {
            if (parent->ptrace & PT_TRACE_FORK)
            {
                // 设置ptrace
                if (child == child->group_leader)
                {
                    __ptrace_link(child, parent->parent);
                }
                parent->ptrace_message = child_pid;
                ptrace_notify((PTRACE_EVENT_FORK << 8) | SIGTRAP);
            }
        }
    }

    if (clone_flags & CLONE_VFORK)
    {
        child->vfork_done = &vfork;
        TTOS_InitCompletion(&vfork);

        /* 等待vfork创建的子进程退出或执行execve */
        TTOS_WaitCompletionUninterruptible(&vfork, TTOS_WAIT_FOREVER);
        if ((parent->ptrace & (PT_PTRACED | PT_TRACE_VFORK_DONE)) ==
            (PT_PTRACED | PT_TRACE_VFORK_DONE))
        {
            ptrace_notify((PTRACE_EVENT_VFORK_DONE << 8) | SIGTRAP);
        }
    }
    else
    {
        ttosRotateRunningTask(ttosGetRunningTask());
    }

    return child_pid;
}
