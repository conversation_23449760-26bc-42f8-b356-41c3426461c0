/**
 * @file    kernel/memory/mmu.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * 009a1ffb 2024-07-17 回退mmu的更改
 * f8ec2a08 2024-07-17
 * 修改由于内存不足导致异常的问题，同时完善mmu的锁的问题，mmap返回值的问题
 * ac006b61 2024-07-02 移除一级ttos目录
 * dd755b67 2024-06-20 增加内核栈保护
 * f5288453 2024-06-17 添加kasan
 * 0d2892eb 2024-06-13 修复由于mmu区域链未保护导致可能的异常
 * b112a8bb 2024-05-21 添加virtio mmio网卡 - 添加网卡驱动 - NAT TAP设备创建脚本
 * - 更新qemu启动参数 - 修正mmu地址计算 b041d869 2024-05-15
 * 格式化代码并处理一些头文件依赖问题 9ac51b03 2024-05-14
 * 更新MMU的内核线性映射空间 2291717e 2024-05-14 适配virtio bus 17dc37fe
 * 2024-05-10 修改拼写错误 aff199ac 2024-05-07 添加pl011的正式驱动 f9627cb8
 * 2024-04-24 增加map 参数检查 54dc901e 2024-04-24 修复用户态进程异常 67370625
 * 2024-04-24 整理了进程 的相关代码 b2ca79ac 2024-04-23 修复mmu在用户态的映射
 * 4fbd5c22 2024-04-22 添加elf加载
 * 0c91b60f 2024-04-19 增加进程对象的创建
 * d6c43fc4 2024-04-19 添加进程
 * 7c710ad1 2024-04-01 添加mmu打印信息
 * fcfe638a 2024-03-31 mmu增加设置属性接口
 * b485c2f5 2024-03-31 开启MMU后 从核启动成功
 * 3d961570 2024-03-30 修复 浮点初始化 增加MMU
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */
#include <list.h>
#include <memblock.h>
#include <mmu.h>
#include <page.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/types.h>
#include <ttosMM.h>
#include <uaccess.h>

#include <system/kconfig.h>

#define KLOG_TAG "MM"
#include <klog.h>
extern int printk (const char *fmt, ...);
extern struct mmu_ops *arch_get_mmu_ops(void);

struct va_range
{
    virt_addr_t start;
    virt_addr_t end;
};

static struct mm g_kernel_mm = {.mmu_table_base = 0,
                                .asid = 0,
                                .mm_region_list = LIST_HEAD_INIT(g_kernel_mm.mm_region_list),
                                .mm_region_type = MM_REGION_TYPE_KERNEL,
                                .page_size = 0,
                                .ops = NULL};

struct mm *get_kernel_mm(void)
{
    return &g_kernel_mm;
}

phys_addr_t zone_normal_pvoffset(void)
{
    return g_kernel_mm.pv_offset;
}

static struct mm_region kernel_linear_map_region = {
    .list = LIST_HEAD_INIT(kernel_linear_map_region.list),
    .physical_address = 0,
    .mem_attr = MT_RWX_DATA | MT_KERNEL,
    .is_need_free = false,
    .is_page_free = false,
};

static inline int mm_kernel_region_map(struct mm_region *region);
static struct mm_region *va2region_r(struct list_head *head, virt_addr_t vaddr);

size_t kernel_mmu_get_page_size(void)
{
    return g_kernel_mm.page_size;
}

void kernel_mmu_set_pvoffset(uint32_t pa_l, uint32_t pa_h, virt_addr_t va)
{
    phys_addr_t pa = (phys_addr_t)pa_l | (phys_addr_t)pa_h << 32;
    g_kernel_mm.pv_offset = pa - va;
}

int mm_init(struct mm *mm)
{
    INIT_SPIN_LOCK(&mm->lock);
    mm->ops = arch_get_mmu_ops();
    return mm->ops->init(mm);
}

int mm_destroy(struct mm *mm)
{
    // long flags;
    // spin_lock_irqsave(&mm->lock, flags);
    while (!list_empty(&mm->mm_region_list))
    {
        mm_region_unmap(mm, list_entry(list_first(&mm->mm_region_list), struct mm_region, list));
    }
    // spin_unlock_irqrestore(&mm->lock, flags);
    mm->ops->deinit(mm);
    return 0;
}

int mm_foreach_region(struct mm *mm,
                      void (*func)(struct mm *mm, struct mm_region *region, void *ctx), void *ctx)
{
    struct mm_region *region_pos;
    spin_lock(&mm->lock);
    list_for_each_entry(region_pos, &mm->mm_region_list, list)
    {
        func(mm, region_pos, ctx);
    }
    spin_unlock(&mm->lock);
    return 0;
}

int kernel_mmu_init(void)
{
    int ret = 0;
    mm_init(&g_kernel_mm);

    kernel_linear_map_region.physical_address = min_low_pfn << g_kernel_mm.page_size_shift;
    kernel_linear_map_region.virtual_address =
        kernel_linear_map_region.physical_address - g_kernel_mm.pv_offset;
    kernel_linear_map_region.region_page_count = (max_low_pfn - min_low_pfn + 1);

    ret = mm_kernel_region_map(&kernel_linear_map_region);

    if (ret != 0)
    {
        return ret;
    }

    g_kernel_mm.ops->apply_change(&g_kernel_mm);
    g_kernel_mm.ops->switch_space(NULL, &g_kernel_mm);

    return 0;
}

int mm_switch_space_to(struct mm *mm)
{
    return mm->ops->switch_space(NULL, mm);
}

void mm_region_init(struct mm_region *region, uintptr_t attr, phys_addr_t pa, size_t size)
{
    region->mem_attr = attr;
    region->physical_address = pa;
    region->region_page_count = PAGE_ALIGN(size) >> g_kernel_mm.page_size_shift;
    region->virtual_address = 0;
    region->flags = 0;
    region->map_min = 0;
    region->filepath = NULL;
    region->offset = 0;
    region->is_need_free = false;
    region->is_page_free = false;
    region->do_fork = NULL;
    region->munmap = NULL;
}

static int compare_ranges(const void *a, const void *b)
{
    const struct va_range *ra = (const struct va_range *)a;
    const struct va_range *rb = (const struct va_range *)b;
    if (ra->start < rb->start) return -1;
    if (ra->start > rb->start) return 1;
    return 0;
}


static virt_addr_t mm_find_free_va_region_by_list(struct mm *mm, size_t page_count, virt_addr_t search_start, virt_addr_t search_end)
{


    if (page_count == 0) return 0;

    size_t needed_size = page_count * PAGE_SIZE;
    size_t region_count = 0;
    struct mm_region *region;
    virt_addr_t result_va = -1;
    int i = 0;

    /* 计算链表中有多少个区域 */
    list_for_each_entry(region, &mm->mm_region_list, list)
    {
        region_count++;
    }

    if (region_count == 0)
    {
        return ((search_end - search_start) >= needed_size) ? search_start : 0;
    }

    struct va_range *ranges = calloc(1, region_count * sizeof(struct va_range));

    /* 将无序链表中的信息复制到数组中 */
    list_for_each_entry(region, &mm->mm_region_list, list)
    {
        ranges[i].start = region->virtual_address;
        ranges[i].end = region->virtual_address + (region->region_page_count * PAGE_SIZE);
        i++;
    }

    qsort(ranges, region_count, sizeof(struct va_range), compare_ranges);

    /* 在已排序的数组上执行间隙查找 */
    virt_addr_t search_cursor = search_start;

    for (i = 0; i < region_count; i++)
    {
        if (ranges[i].start > search_cursor)
        {
            size_t gap_size = ranges[i].start - search_cursor;

            if (gap_size >= needed_size)
            {
                result_va = search_cursor;
                free(ranges);
                return result_va;
            }
        }
        if (ranges[i].end > search_cursor)
        {
            search_cursor = ranges[i].end;
        }
    }

    if (search_end > search_cursor)
    {
        size_t final_gap_size = search_end - search_cursor;
        if (final_gap_size >= needed_size)
        {
            result_va = search_cursor;
        }
    }

    free(ranges);
    return result_va;
}

int mm_region_map(struct mm *mm, struct mm_region *region)
{
    long flags;
    int find_vaild_varegion = 0;
    int ret = -1;
    if (region->flags & MAP_FIXED)
    {
        struct mm_region *old_region = va2region_r(&mm->mm_region_list, region->map_min);
        if (old_region != NULL)
        {
            region->virtual_address = region->map_min;
            mm_region_modify(mm, region, true);
        }
    }

    if ((region->virtual_address == 0 )
        && (mm->mm_region_type == MM_REGION_TYPE_USER)
        && page_allocer_inited()
        && ((region->flags & MAP_FIXED) == 0))
    {
        virt_addr_t va_start = region->map_min ? region->map_min : USER_SPACE_START;
        if (va_start < MMAP_START_VADDR)
        {
            va_start = MMAP_START_VADDR;
        }
        virt_addr_t  va_end = USER_SPACE_END + 1;
        spin_lock_irqsave(&mm->lock, flags);
        region->virtual_address = mm_find_free_va_region_by_list(mm, region->region_page_count, va_start, va_end);
        ret = mm->ops->map(mm, region);
        spin_unlock_irqrestore(&mm->lock, flags);
    }
    else
    {
        ret = mm->ops->map(mm, region);
    }
    
    if (ret == 0)
    {
        spin_lock_irqsave(&mm->lock, flags);
        list_add(&region->list, &mm->mm_region_list);
        spin_unlock_irqrestore(&mm->lock, flags);
    }
    return ret;
}
extern uintptr_t g_tmp_fp;
int mm_region_unmap(struct mm *mm, struct mm_region *region)
{
    if (region == NULL || !kernel_access_check(region, sizeof(*region), UACCESS_R))
    {
        return -1;
    }
    if (region->list.next == NULL ||
        !kernel_access_check(region->list.next, sizeof(*region->list.next), UACCESS_R))
    {
        return -1;
    }
    int ret = mm->ops->unmap(mm, region);
    if (ret == 0)
    {
        long flags;
        spin_lock_irqsave(&mm->lock, flags);
        list_del(&region->list);
        spin_unlock_irqrestore(&mm->lock, flags);
    }
    if (region->munmap)
    {
        region->munmap(region);
    }
    if (region->flags & MAP_IS_FILE)
    {
        if (region->filepath)
        {
            region->flags = 0;
            free(region->filepath);
            region->filepath = NULL;
        }
    }
    if (region->is_page_free)
    {
        pages_free(region->physical_address, region->page_order);
    }
    if (region->is_need_free)
    {
        free(region);
    }
    return ret;
}

int mm_region_modify(struct mm *mm, struct mm_region *region, bool is_unmap)
{
    virt_addr_t border1, border2;
    struct mm_region *old_region;
    irq_flags_t flags;

    size_t maped1_page_count, maped2_page_count;

    /* 计算修改边界1 */
    border1 = region->virtual_address;

    /* 计算修改边界2 */
    border2 = region->virtual_address + region->region_page_count * ttosGetPageSize();

    spin_lock_irqsave(&mm->lock, flags);

    /* 获取原来的映射区域 */
    old_region = va2region_r(&mm->mm_region_list, border1);

    /* 检查修改区域是否在同一映射区域内 */
    if (old_region != va2region_r(&mm->mm_region_list, border2 - 1))
    {
        spin_unlock_irqrestore(&mm->lock, flags);
        return -1;
    }

    /* 检查是否已映射 */
    if (old_region == NULL)
    {
        spin_unlock_irqrestore(&mm->lock, flags);
        return -1;
    }

    if (is_unmap)
    {
        /* 后面会对页的引用计数减1 此时不能释放，否则页就没了 */
        mm->ops->unmap(mm, region);

        /* 移除旧的区域的管理块 */
        list_del(&old_region->list);
    }
    else
    {
        /* 申请新的映射区域 */
        struct mm_region *map_region = malloc(sizeof(struct mm_region));
        if (map_region == NULL)
        {
            spin_unlock_irqrestore(&mm->lock, flags);
            return -1;
        }

        /* 拷贝新的映射区域 */
        memcpy(map_region, old_region, sizeof(struct mm_region));
        map_region->is_need_free = true;
        map_region->region_page_count = region->region_page_count;
        map_region->virtual_address = region->virtual_address;
        map_region->physical_address = region->physical_address;
        map_region->mem_attr = region->mem_attr;
        map_region->map_min = map_region->virtual_address;

        if (map_region->flags & MAP_IS_FILE)
        {
            map_region->filepath = strdup(old_region->filepath);
        }

        /* 执行属性修改 */
        mm->ops->modify(mm, map_region);

        /* 移除旧的区域的管理块 */
        list_del(&old_region->list);
        list_add(&map_region->list, &mm->mm_region_list);
    }

    /* 计算原区域边界1前的页大小 */
    maped1_page_count = (border1 - old_region->virtual_address) / ttosGetPageSize();

    if (maped1_page_count)
    {
        struct mm_region *map_region1 = malloc(sizeof(struct mm_region));
        if (map_region1 == NULL)
        {
            spin_unlock_irqrestore(&mm->lock, flags);
            return -1;
        }
        memcpy(map_region1, old_region, sizeof(struct mm_region));
        map_region1->is_need_free = true;
        map_region1->region_page_count = maped1_page_count;

        if (map_region1->is_page_free)
        {
            /* 有边界1映射，将页组的引用计数+1 */
            page_ref_inc(map_region1->physical_address, map_region1->page_order);
        }

        if (map_region1->flags & MAP_IS_FILE)
        {
            map_region1->filepath = strdup(old_region->filepath);
            map_region1->offset =
                PAGE_ALIGN_DOWN(old_region->offset) + (border1 - old_region->virtual_address);
        }

        /* 添加区域1的管理块 */
        list_add(&map_region1->list, &mm->mm_region_list);
    }

    /* 计算原区域边界2后的页大小 */
    maped2_page_count = (old_region->virtual_address +
                         old_region->region_page_count * ttosGetPageSize() - border2) /
                        ttosGetPageSize();
    if (maped2_page_count)
    {
        struct mm_region *map_region2 = malloc(sizeof(struct mm_region));
        if (map_region2 == NULL)
        {
            spin_unlock_irqrestore(&mm->lock, flags);
            return -1;
        }
        memcpy(map_region2, old_region, sizeof(struct mm_region));
        map_region2->is_need_free = true;
        map_region2->region_page_count = maped2_page_count;
        map_region2->map_min = map_region2->virtual_address = border2;
        map_region2->physical_address =
            old_region->physical_address + (border2 - old_region->virtual_address);

        if (map_region2->flags & MAP_IS_FILE)
        {
            map_region2->filepath = strdup(old_region->filepath);
            map_region2->offset =
                PAGE_ALIGN_DOWN(old_region->offset) + (border2 - old_region->virtual_address);
        }

        if (map_region2->is_page_free)
        {
            /* 有边界2映射，将页组的引用计数+1 */
            page_ref_inc(map_region2->physical_address, map_region2->page_order);
        }

        /* 添加区域2的管理块 */
        list_add(&map_region2->list, &mm->mm_region_list);
    }

    if (is_unmap && old_region->is_page_free)
    {
        /* 如果是 unmap 则需要对引用计数-1 */
        pages_free(old_region->physical_address, old_region->page_order);
    }

    if (old_region->is_need_free)
    {
        if (old_region->flags & MAP_IS_FILE)
        {
            if (old_region->filepath)
            {
                free(old_region->filepath);
            }
        }
        free(old_region);
        old_region = NULL;
    }

    spin_unlock_irqrestore(&mm->lock, flags);

    return 0;
}

static inline int mm_kernel_region_map(struct mm_region *region)
{
    return mm_region_map(&g_kernel_mm, region);
}

static inline int mm_kernel_region_unmap(struct mm_region *region)
{
    return mm_region_unmap(&g_kernel_mm, region);
}

static struct mm_region *va2region_r(struct list_head *head, virt_addr_t vaddr)
{
    struct list_head *pos;
    struct mm_region *region;
    if (list_empty(head))
    {
        return NULL;
    }
    list_for_each(pos, head)
    {
        region = list_entry(pos, struct mm_region, list);
        if (region->virtual_address <= vaddr &&
            (region->virtual_address + region->region_page_count * PAGE_SIZE) > vaddr)
        {
            return region;
        }
    }
    return NULL;
}

struct mm_region *mm_va2region(struct mm *mm, virt_addr_t vaddr)
{
    struct mm_region *ret;
    long flags;
    spin_lock_irqsave(&mm->lock, flags);
    ret = va2region_r(&mm->mm_region_list, vaddr);
    spin_unlock_irqrestore(&mm->lock, flags);
    return ret;
}

volatile void *ioremap(phys_addr_t io_base, size_t size)
{
    struct mm_region *region;

    phys_addr_t io_map_base = PAGE_ALIGN_DOWN(io_base);

    region = (struct mm_region *)malloc(sizeof(struct mm_region));

    mm_region_init(region, MT_DEVICE | MT_KERNEL, io_map_base, PAGE_ALIGN(size));
    region->is_need_free = true;
    mm_kernel_region_map(region);

    return (volatile void *)region->virtual_address + (io_base - io_map_base);
}

void iounmap(virt_addr_t vaddr)
{
    struct mm_region *region;
    region = mm_va2region(&g_kernel_mm, vaddr);
    if (region != NULL)
    {
        mm_kernel_region_unmap(region);
    }
}

phys_addr_t mm_v2p(struct mm *mm, virt_addr_t va)
{
    return g_kernel_mm.ops->v2p(mm, va);
}

phys_addr_t mm_kernel_v2p(virt_addr_t va)
{
    return mm_v2p(&g_kernel_mm, va);
}

virt_addr_t mm_kernel_p2v(phys_addr_t pa)
{
    if (page_address(PAGE_ALIGN_DOWN(pa)))
        return pa - g_kernel_mm.pv_offset;
    return 0;
}

int ttosSetPageAttribute(virt_addr_t va, size_t size, uint64_t attr)
{
    struct mm_region region;
    mm_region_init(&region, attr, 0, size);
    region.virtual_address = va;
    region.physical_address = mm_kernel_v2p(va);

    return mm_region_modify(&g_kernel_mm, &region, false);
}

#ifdef CONFIG_SHELL

#include <shell.h>

static int shell_out(const char *str)
{
    int ret;
    Shell *shell = shellGetCurrent();
    SHELL_LOCK(shell);
    ret = shellWriteString(shell, str);
    SHELL_UNLOCK(shell);
    return ret;
}

int mmu_print_table_command(int argc, const char *argv[])
{
    return g_kernel_mm.ops->print_table(&g_kernel_mm, shell_out);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_MAIN) |
                     SHELL_CMD_DISABLE_RETURN,
                 mmu, mmu_print_table_command, print mmu kernel table);
#endif
