
menu "memory"

choice
    prompt "memory alloc algorithm"
    default SLUB
	
config TLS<PERSON>
    bool "tlsf"

config WORKSPACE
    bool "ttosworkspace"

config SLUB
    bool "Slub"
	
endchoice

if !SLUB

config KERNEL_HEAP_SIZE
    hex "kernel heap size"
    default 0x10000000

config KERNEL_NC_HEAP_SIZE
    hex "kernel DMA heap size"
    default 0x100000

endif

config MM_KASAN
	bool "Kernel Address Sanitizer"
	default n
	---help---
		KASan is a fast compiler-based tool for detecting memory
		bugs in native code. After turn on this option, Please
		add -fsanitize=kernel-address to CFLAGS/CXXFLAGS too.

config MM_KASAN_ALL
	bool "Enable KASan for the entire image"
	depends on MM_KASAN
	default y
	---help---
		This option activates address sanitizer for the entire image.
		If you don't enable this option, you have to explicitly specify
		"-fsanitize=kernel-address" for the files/directories you want
		to check. Enabling this option will get image size increased
		and performance decreased significantly.

config M<PERSON>_KASAN_DISABLE_READS_CHECK
	bool "Disable reads check"
	depends on MM_<PERSON>ASAN
	default n
	---help---
		This option disable kasan reads check. It speeds up performance
		compared with default read/write check. Only disable it when you are
		sure there's no need to do so. Or performance is too bad and only focus
		on writes check.

config MM_KASAN_DISABLE_WRITES_CHECK
	bool "Disable writes check"
	depends on MM_KASAN
	default n
	---help---
		This option disable kasan writes check.

config MM_KASAN_GLOBAL
	bool "Enable global data check"
	depends on MM_KASAN
	default y
	---help---
		This option enables KASan global data check.
		It's used to extract segments in the linker script.
		Two new segments need to be created, one being
		".kasan.unused: { *(.data..LASANLOC*) }",
		used to eliminate excess data generated.
		One is ".kasan.global:{
		KEEP ( *(. data.. LASAN0))
		KEEP ( *(. data. rel. local.. LASAN0))
		}", used to extract data generated by the compiler

config MM_UBSAN
	bool "Undefined Behavior Sanitizer"
	default n
	---help---
		UBSan is a fast undefined behavior detector. UBSan modifies
		the program at compile-time to catch various kinds of
		undefined behavior during program execution

config MM_UBSAN_ALL
	bool "Enable UBSan for the entire image"
	depends on MM_UBSAN
	default y
	---help---
		This option activates UBSan instrumentation for the
		entire image. If you don't enable this option, you have to
		explicitly specify "-fsanitize=undefined" for
		the files/directories you want to check. Enabling this option
		will get image size increased and performance decreased
		significantly.

config MM_UBSAN_OPTION
	string "UBSan options"
	depends on MM_UBSAN
	default "-fsanitize=undefined"
	---help---
		This option activates specified UBSan instrumentation. Please
		refer to https://clang.llvm.org/docs/UndefinedBehaviorSanitizer.html
		for available options.

config MM_UBSAN_TRAP_ON_ERROR
	bool "Enable UBSan trap on error to crash immediately"
	depends on MM_UBSAN
	default n
	---help---
		The undefined instruction trap should cause your program to crash,
		save the code space significantly.

endmenu
